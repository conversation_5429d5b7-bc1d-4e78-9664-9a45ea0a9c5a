import os
import json
import time
import pymysql
import pymysql.cursors
from contextlib import contextmanager
from typing import List, Dict, Optional, Any
import logging
from dotenv import load_dotenv
import threading
import queue

# 导入自定义日志模块
from logger_config import (
    log_db_operation, log_error
)

# 导入缓存配置
from cache_config import cache_manager, cache_result

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# MySQL数据库配置
DB_CONFIG = {
    'host': os.getenv('MYSQL_HOST', 'localhost'),
    'port': int(os.getenv('MYSQL_PORT', 3306)),
    'user': os.getenv('MYSQL_USER', 'root'),
    'password': os.getenv('MYSQL_PASSWORD', ''),
    'database': os.getenv('MYSQL_DATABASE', 'cnpcklwc'),
    'charset': 'utf8mb4',
    'autocommit': True,
    'cursorclass': pymysql.cursors.DictCursor,
}

class DatabaseManager:
    """数据库管理器 - MySQL版本"""

    def __init__(self):
        self.connection_pool = None
        self._pool_lock = threading.Lock()
        self._init_database()
        self._init_connection_pool()
    
    def _extract_table_name(self, sql: str) -> str:
        """从SQL语句中提取表名"""
        try:
            import re
            sql_upper = sql.upper().strip()
            
            # 匹配 INSERT INTO table_name
            insert_match = re.search(r'INSERT\s+INTO\s+([^\s\(]+)', sql_upper)
            if insert_match:
                return insert_match.group(1).split('.')[-1].lower()
            
            # 匹配 UPDATE table_name
            update_match = re.search(r'UPDATE\s+([^\s]+)', sql_upper)
            if update_match:
                return update_match.group(1).split('.')[-1].lower()
            
            # 匹配 DELETE FROM table_name
            delete_match = re.search(r'DELETE\s+FROM\s+([^\s]+)', sql_upper)
            if delete_match:
                return delete_match.group(1).split('.')[-1].lower()
            
            # 匹配 SELECT ... FROM table_name
            select_match = re.search(r'FROM\s+([^\s,\)]+)', sql_upper)
            if select_match:
                table_name = select_match.group(1)
                # 处理别名 (如 FROM users u)
                if ' ' in table_name:
                    table_name = table_name.split()[0]
                return table_name.split('.')[-1].lower()
            
            return "unknown"
        except:
            return "unknown"
    
    def _init_database(self):
        """初始化数据库连接"""
        try:
            success = self.test_connection()
            if success:
                logger.info("MySQL数据库连接初始化成功")
            else:
                logger.error("MySQL数据库连接测试失败")
                raise Exception("MySQL数据库连接测试失败")
        except Exception as e:
            logger.error(f"MySQL数据库初始化失败: {e}")
            raise
    
    def _init_connection_pool(self):
        """初始化数据库连接池"""
        try:
            # 从环境变量获取连接池配置
            min_conn = int(os.getenv('DB_POOL_MIN_CONN', 5))
            max_conn = int(os.getenv('DB_POOL_MAX_CONN', 20))

            # 创建简单的连接池
            self.connection_pool = queue.Queue(maxsize=max_conn)
            self.max_connections = max_conn
            self.current_connections = 0

            # 预创建最小连接数
            for _ in range(min_conn):
                conn = pymysql.connect(**DB_CONFIG)
                self.connection_pool.put(conn)
                self.current_connections += 1

            logger.info(f"MySQL数据库连接池初始化成功 - 最小连接数: {min_conn}, 最大连接数: {max_conn}")

        except Exception as e:
            logger.error(f"MySQL数据库连接池初始化失败: {e}")
            self.connection_pool = None
            raise
    
    def test_connection(self):
        """测试数据库连接"""
        start_time = time.time()
        try:
            connection = pymysql.connect(**DB_CONFIG)
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
            connection.close()
            duration = time.time() - start_time
            log_db_operation("test_connection", "system", duration=duration, status='success')
            return result is not None
        except Exception as e:
            duration = time.time() - start_time
            log_db_operation("test_connection", "system", duration=duration, status='failed', error=str(e))
            log_error(f"MySQL数据库连接测试失败: {e}", exc_info=True)
            logger.error(f"MySQL数据库连接测试失败: {e}")
            return False
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接上下文管理器 - 使用连接池"""
        connection = None
        try:
            if self.connection_pool:
                try:
                    # 从连接池获取连接
                    connection = self.connection_pool.get(timeout=5)
                    # 测试连接是否有效
                    connection.ping(reconnect=True)
                    yield connection
                except queue.Empty:
                    # 连接池为空，创建新连接
                    if self.current_connections < self.max_connections:
                        connection = pymysql.connect(**DB_CONFIG)
                        self.current_connections += 1
                        yield connection
                    else:
                        raise Exception("连接池已满，无法创建新连接")
            else:
                # 降级到直接连接
                connection = pymysql.connect(**DB_CONFIG)
                yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"数据库操作异常: {e}")
            raise
        finally:
            if connection:
                if self.connection_pool and not self.connection_pool.full():
                    # 归还连接到连接池
                    self.connection_pool.put(connection)
                else:
                    # 关闭连接
                    connection.close()
                    if hasattr(self, 'current_connections'):
                        self.current_connections -= 1
    
    def execute_query(self, sql: str, params: tuple = None, user_id: str = None, context: str = None) -> List[Dict]:
        """执行查询SQL"""
        start_time = time.time()
        table_name = self._extract_table_name(sql)
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, params)
                    result = cursor.fetchall()
                    duration = time.time() - start_time
                    log_db_operation("query", table_name, user_id=user_id, duration=duration, status='success',
                                   sql_preview=sql[:100], row_count=len(result), context=context)
                    return result
        except Exception as e:
            duration = time.time() - start_time
            log_db_operation("query", table_name, user_id=user_id, duration=duration, status='failed',
                           error=str(e), sql_preview=sql[:100], context=context)
            log_error(f"查询执行失败: {str(e)}", exc_info=True, user_id=user_id)
            raise
    
    def execute_update(self, sql: str, params: tuple = None, user_id: str = None, context: str = None) -> int:
        """执行更新SQL，返回影响的行数"""
        start_time = time.time()
        table_name = self._extract_table_name(sql)
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, params)
                    rowcount = cursor.rowcount
                    duration = time.time() - start_time
                    log_db_operation("update", table_name, user_id=user_id, duration=duration, status='success', 
                                   sql_preview=sql[:100], affected_rows=rowcount, context=context)
                    return rowcount
        except Exception as e:
            duration = time.time() - start_time
            log_db_operation("update", table_name, user_id=user_id, duration=duration, status='failed', 
                           error=str(e), sql_preview=sql[:100], context=context)
            log_error(f"更新执行失败: {str(e)}", exc_info=True, user_id=user_id)
            raise
    
    def execute_insert(self, sql: str, params: tuple = None, user_id: str = None, context: str = None) -> int:
        """执行插入SQL，返回新插入记录的ID"""
        start_time = time.time()
        table_name = self._extract_table_name(sql)
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, params)
                    new_id = cursor.lastrowid
                    duration = time.time() - start_time
                    log_db_operation("insert", table_name, user_id=user_id, duration=duration, status='success',
                                   sql_preview=sql[:100], new_id=new_id, context=context)
                    return new_id
        except Exception as e:
            duration = time.time() - start_time
            log_db_operation("insert", table_name, user_id=user_id, duration=duration, status='failed',
                           error=str(e), sql_preview=sql[:100], context=context)
            log_error(f"插入执行失败: {str(e)}", exc_info=True, user_id=user_id)
            raise
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        if not self.connection_pool:
            return {"status": "disabled", "message": "连接池未启用"}

        try:
            # 获取连接池状态
            available_connections = self.connection_pool.qsize()
            used_connections = self.current_connections - available_connections

            stats = {
                "status": "active",
                "max_connections": self.max_connections,
                "current_connections": self.current_connections,
                "available_connections": available_connections,
                "used_connections": used_connections,
                "pool_utilization": round((used_connections / self.max_connections) * 100, 2) if self.max_connections > 0 else 0
            }
            return stats
        except Exception as e:
            logger.error(f"获取连接池统计失败: {e}")
            return {"status": "error", "message": str(e)}
    
    def close_pool(self):
        """关闭连接池"""
        if self.connection_pool:
            try:
                # 关闭所有连接
                while not self.connection_pool.empty():
                    conn = self.connection_pool.get_nowait()
                    conn.close()
                logger.info("MySQL数据库连接池已关闭")
            except Exception as e:
                logger.error(f"关闭连接池失败: {e}")
    
    def __del__(self):
        """析构函数，确保连接池正确关闭"""
        self.close_pool()

class UserManager:
    """用户管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def get_or_create_user(self, userid: str, username: str = None) -> Dict:
        """获取或创建用户"""
        start_time = time.time()
        try:
            # 先尝试获取用户
            user = self.get_user(userid)
            if user:
                # 更新最后活跃时间
                self.update_last_active(userid)
                duration = time.time() - start_time
                log_db_operation("get_user", "users", user_id=userid, duration=duration, status='success')
                return user
            
            # 用户不存在，创建新用户
            username = username or f"用户_{userid}"
            email = f"{userid}@kunlun.com"
            sql = """
                INSERT INTO users (userid, username, email, avatar, last_active) 
                VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
            """
            self.db.execute_insert(sql, (userid, username, email, 'User'), user_id=userid, context="create_user")
            
            # 返回新创建的用户
            new_user = self.get_user(userid)
            duration = time.time() - start_time
            log_db_operation("create_user", "users", user_id=userid, duration=duration, 
                           status='success', username=username)
            return new_user
            
        except Exception as e:
            duration = time.time() - start_time
            log_db_operation("get_or_create_user", "users", user_id=userid, duration=duration, 
                           status='failed', error=str(e))
            log_error(f"获取或创建用户失败 userid={userid}: {e}", exc_info=True, user_id=userid)
            logger.error(f"获取或创建用户失败 userid={userid}: {e}")
            raise
    
    def get_user(self, userid: str) -> Optional[Dict]:
        """根据userid获取用户信息"""
        # 先尝试从缓存获取
        cached_user = cache_manager.get('user_info', userid)
        if cached_user:
            return cached_user
        
        # 从数据库查询
        sql = "SELECT * FROM users WHERE userid = %s AND status = 1"
        result = self.db.execute_query(sql, (userid,), user_id=userid, context="get_user")
        user_data = result[0] if result else None
        
        # 存入缓存
        if user_data:
            cache_manager.set('user_info', userid, user_data)
        
        return user_data
    
    def update_last_active(self, userid: str):
        """更新用户最后活跃时间"""
        sql = "UPDATE users SET last_active = CURRENT_TIMESTAMP WHERE userid = %s"
        self.db.execute_update(sql, (userid,), user_id=userid, context="update_last_active")
        
        # 清除用户缓存，强制下次重新加载
        cache_manager.delete('user_info', userid)

class SessionManager:
    """会话管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def create_session(self, session_id: str, userid: str, title: str = None) -> str:
        """创建新会话"""
        try:
            title = title or "新对话"
            sql = """
                INSERT INTO chat_sessions (session_id, userid, title)
                VALUES (%s, %s, %s)
                ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP
            """
            self.db.execute_insert(sql, (session_id, userid, title), user_id=userid, context="create_session")
            
            # 清除用户会话列表缓存
            cache_manager.delete_pattern(f"kunlun:sessions:{userid}_*")
            
            return session_id
        except Exception as e:
            logger.error(f"创建会话失败 session_id={session_id}: {e}")
            raise
    
    def get_user_sessions(self, userid: str, limit: int = 50) -> List[Dict]:
        """获取用户的会话列表"""
        # 先尝试从缓存获取
        cache_key = f"{userid}_{limit}"
        cached_sessions = cache_manager.get('user_sessions_list', cache_key)
        if cached_sessions:
            return cached_sessions
        
        sql = """
            SELECT s.*, 
                   (SELECT content FROM chat_messages m 
                    WHERE m.session_id = s.session_id AND m.role = 'user' 
                    ORDER BY m.created_at ASC LIMIT 1) as first_user_message,
                   (SELECT COUNT(*) FROM chat_messages m 
                    WHERE m.session_id = s.session_id) as message_count
            FROM chat_sessions s 
            WHERE s.userid = %s AND s.is_deleted = 0 
            ORDER BY s.updated_at DESC 
            LIMIT %s
        """
        try:
            results = self.db.execute_query(sql, (userid, limit), user_id=userid, context="get_user_sessions")
            # 确保每个结果都有正确的字段
            for result in results:
                if result.get('first_user_message') is None:
                    result['first_user_message'] = ""
                if result.get('message_count') is None:
                    result['message_count'] = 0
            
            # 存入缓存
            cache_manager.set('user_sessions_list', cache_key, results, expire=300)  # 5分钟缓存
            
            return results
        except Exception as e:
            logger.error(f"获取用户会话失败: {e}")
            return []
    
    def update_session_title(self, session_id: str, title: str):
        """更新会话标题"""
        sql = "UPDATE chat_sessions SET title = %s, updated_at = CURRENT_TIMESTAMP WHERE session_id = %s"
        self.db.execute_update(sql, (title, session_id), context="update_session_title")
    
    def delete_session(self, session_id: str, userid: str):
        """删除会话（软删除）"""
        sql = "UPDATE chat_sessions SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP, deleted_at = CURRENT_TIMESTAMP WHERE session_id = %s AND userid = %s"
        self.db.execute_update(sql, (session_id, userid), user_id=userid, context="delete_session")

class MessageManager:
    """消息管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def add_message(self, session_id: str, userid: str, role: str, content: str, 
                    message_type: str = 'text', metadata: Dict = None) -> int:
        """添加消息"""
        try:
            # 获取下一个序号
            sequence_num = self.get_next_sequence_num(session_id)
            
            # 处理metadata为JSON
            metadata_json = json.dumps(metadata) if metadata else None
            
            sql = """
                INSERT INTO chat_messages (session_id, userid, role, content, message_type, metadata, sequence_num) 
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            message_id = self.db.execute_insert(sql, (session_id, userid, role, content, message_type, metadata_json, sequence_num), user_id=userid, context="add_message")
            
            # 更新会话时间戳
            self.update_session_timestamp(session_id)
            
            # 清除相关缓存
            cache_manager.delete_pattern(f"kunlun:messages:{session_id}_*")
            cache_manager.delete_pattern(f"kunlun:sessions:{userid}_*")
            
            return message_id
        except Exception as e:
            logger.error(f"添加消息失败 session_id={session_id}: {e}")
            raise
    
    def add_message_with_components(self, session_id: str, userid: str, role: str, content: str, 
                                  tables: List[str] = None, plots: List[str] = None, 
                                  message_type: str = 'rich') -> int:
        """添加包含表格和图表组件的消息"""
        try:
            # 构建metadata
            metadata = {
                'has_tables': bool(tables),
                'has_plots': bool(plots),
                'table_count': len(tables) if tables else 0,
                'plot_count': len(plots) if plots else 0,
                'components': {}
            }
            
            # 添加表格数据
            if tables:
                metadata['components']['tables'] = tables
                
            # 添加图表数据
            if plots:
                metadata['components']['plots'] = plots
            
            # 调用原有的add_message方法
            return self.add_message(session_id, userid, role, content, message_type, metadata)
            
        except Exception as e:
            logger.error(f"添加富文本消息失败 session_id={session_id}: {e}")
            raise
    
    def get_session_messages(self, session_id: str, limit: int = 100) -> List[Dict]:
        """获取会话的所有消息"""
        # 先尝试从缓存获取
        cache_key = f"{session_id}_{limit}"
        cached_messages = cache_manager.get('session_messages', cache_key)
        if cached_messages:
            return cached_messages
        
        sql = """
            SELECT m.*, u.username, u.avatar
            FROM chat_messages m
            LEFT JOIN users u ON m.userid = u.userid
            WHERE m.session_id = %s
            ORDER BY m.sequence_num ASC, m.created_at ASC
            LIMIT %s
        """
        messages = self.db.execute_query(sql, (session_id, limit), context="get_session_messages")
        
        # 处理metadata字段
        for msg in messages:
            if msg.get('metadata'):
                try:
                    msg['metadata'] = json.loads(msg['metadata']) if isinstance(msg['metadata'], str) else msg['metadata']
                except:
                    msg['metadata'] = None
        
        # 存入缓存
        cache_manager.set('session_messages', cache_key, messages, expire=600)  # 10分钟缓存
        
        return messages
    
    def get_recent_messages(self, session_id: str, limit: int = 20) -> List[Dict]:
        """获取会话的最近消息"""
        sql = """
            SELECT m.*, u.username, u.avatar
            FROM chat_messages m
            LEFT JOIN users u ON m.userid = u.userid
            WHERE m.session_id = %s
            ORDER BY m.sequence_num DESC, m.created_at DESC
            LIMIT %s
        """
        messages = self.db.execute_query(sql, (session_id, limit), context="get_recent_messages")
        
        # 处理metadata字段并反转顺序
        for msg in messages:
            if msg.get('metadata'):
                try:
                    msg['metadata'] = json.loads(msg['metadata']) if isinstance(msg['metadata'], str) else msg['metadata']
                except:
                    msg['metadata'] = None
        
        return list(reversed(messages))
    
    def get_next_sequence_num(self, session_id: str) -> int:
        """获取下一个消息序号"""
        sql = "SELECT COALESCE(MAX(sequence_num), 0) + 1 as next_num FROM chat_messages WHERE session_id = %s"
        result = self.db.execute_query(sql, (session_id,), context="get_next_sequence_num")
        return result[0]['next_num'] if result else 1
    
    def update_session_timestamp(self, session_id: str):
        """更新会话时间戳"""
        sql = "UPDATE chat_sessions SET updated_at = CURRENT_TIMESTAMP WHERE session_id = %s"
        self.db.execute_update(sql, (session_id,), context="update_session_timestamp")
    
    def clear_session_messages(self, session_id: str, userid: str):
        """清空会话消息"""
        sql = "DELETE FROM chat_messages WHERE session_id = %s AND userid = %s"
        self.db.execute_update(sql, (session_id, userid), user_id=userid, context="clear_session_messages")

class ChatDatabase:
    """聊天数据库统一接口"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.user_manager = UserManager(self.db_manager)
        self.session_manager = SessionManager(self.db_manager)
        self.message_manager = MessageManager(self.db_manager)
        self.security_manager = SecurityManager(self.db_manager)
    
    def init_database(self):
        """初始化数据库（验证连接）"""
        return self.db_manager.test_connection()
    
    def get_user_chat_history(self, userid: str, session_id: str = None) -> Dict:
        """获取用户聊天历史"""
        try:
            # 确保用户存在
            user = self.user_manager.get_or_create_user(userid)
            
            if session_id:
                # 获取特定会话的消息
                messages = self.message_manager.get_session_messages(session_id)
                return {
                    'success': True,
                    'user': user,
                    'session_id': session_id,
                    'messages': messages,
                    'message_count': len(messages)
                }
            else:
                # 获取用户的所有会话
                sessions = self.session_manager.get_user_sessions(userid)
                return {
                    'success': True,
                    'user': user,
                    'sessions': sessions,
                    'session_count': len(sessions)
                }
        except Exception as e:
            logger.error(f"获取用户聊天历史失败 userid={userid}: {e}")
            return {'success': False, 'error': str(e)}
    
    def save_chat_message(self, session_id: str, userid: str, role: str, content: str) -> bool:
        """保存聊天消息"""
        try:
            # 确保用户存在
            self.user_manager.get_or_create_user(userid)
            
            # 确保会话存在
            self.session_manager.create_session(session_id, userid)
            
            # 保存消息
            message_id = self.message_manager.add_message(session_id, userid, role, content)
            
            return message_id is not None
        except Exception as e:
            logger.error(f"保存聊天消息失败 session_id={session_id}: {e}")
            return False
    
    def save_rich_message(self, session_id: str, userid: str, role: str, content: str, 
                         tables: List[str] = None, plots: List[str] = None) -> bool:
        """保存包含表格和图表的富文本消息"""
        try:
            # 确保用户存在
            self.user_manager.get_or_create_user(userid)
            
            # 确保会话存在
            self.session_manager.create_session(session_id, userid)
            
            # 保存富文本消息
            message_id = self.message_manager.add_message_with_components(
                session_id, userid, role, content, tables, plots
            )
            
            return message_id is not None
        except Exception as e:
            logger.error(f"保存富文本消息失败 session_id={session_id}: {e}")
            return False

class SecurityManager:
    """安全防护管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def get_active_keywords(self, category_name: str = None) -> List[Dict]:
        """获取活跃的敏感词列表"""
        # 先尝试从缓存获取
        cache_key = category_name or "all"
        cached_keywords = cache_manager.get('security_keywords', cache_key)
        if cached_keywords:
            return cached_keywords
        
        try:
            if category_name:
                sql = """
                    SELECT k.*, c.name as category_name, c.description as category_description
                    FROM security_keywords k
                    JOIN security_keyword_categories c ON k.category_id = c.id
                    WHERE k.is_active = 1 AND c.is_active = 1 AND c.name = %s
                    ORDER BY k.severity DESC, k.keyword ASC
                """
                result = self.db.execute_query(sql, (category_name,), context="get_active_keywords_by_category")
            else:
                sql = """
                    SELECT k.*, c.name as category_name, c.description as category_description
                    FROM security_keywords k
                    JOIN security_keyword_categories c ON k.category_id = c.id
                    WHERE k.is_active = 1 AND c.is_active = 1
                    ORDER BY k.severity DESC, c.name ASC, k.keyword ASC
                """
                result = self.db.execute_query(sql, context="get_all_active_keywords")
            
            # 存入缓存
            cache_manager.set('security_keywords', cache_key, result)
            
            return result
        except Exception as e:
            logger.error(f"获取敏感词失败: {e}")
            return []
    
    def get_keywords_by_category(self) -> Dict[str, List[str]]:
        """按分类获取敏感词，返回字典格式"""
        try:
            keywords = self.get_active_keywords()
            result = {}
            for keyword in keywords:
                category = keyword['category_name']
                if category not in result:
                    result[category] = []
                result[category].append(keyword['keyword'])
            return result
        except Exception as e:
            logger.error(f"按分类获取敏感词失败: {e}")
            return {}
    
    def get_security_rules(self, rule_type: str = None) -> List[Dict]:
        """获取安全规则配置"""
        try:
            if rule_type:
                sql = """
                    SELECT * FROM security_rules 
                    WHERE is_active = 1 AND rule_type = %s
                    ORDER BY created_at DESC
                """
                return self.db.execute_query(sql, (rule_type,), context="get_security_rules_by_type")
            else:
                sql = """
                    SELECT * FROM security_rules 
                    WHERE is_active = 1
                    ORDER BY rule_type ASC, created_at DESC
                """
                return self.db.execute_query(sql, context="get_all_security_rules")
        except Exception as e:
            logger.error(f"获取安全规则失败: {e}")
            return []
    
    def get_security_config(self) -> Dict:
        """获取完整的安全配置"""
        try:
            # 获取敏感词配置
            keywords = self.get_keywords_by_category()
            
            # 获取安全规则配置
            rules = self.get_security_rules()
            rules_config = {}
            for rule in rules:
                try:
                    config = json.loads(rule['rule_config']) if isinstance(rule['rule_config'], str) else rule['rule_config']
                    rules_config[rule['rule_name']] = config
                except:
                    logger.warning(f"解析安全规则配置失败: {rule['rule_name']}")
            
            return {
                'keywords': keywords,
                'rules': rules_config,
                'timestamp': time.time()
            }
        except Exception as e:
            logger.error(f"获取安全配置失败: {e}")
            return {'keywords': {}, 'rules': {}, 'timestamp': time.time()}
    
    def log_security_event(self, userid: str, session_id: str, event_type: str, 
                          severity: str, content: str = None, detected_keywords: List[str] = None,
                          action_taken: str = None, ip_address: str = None, 
                          user_agent: str = None, metadata: Dict = None) -> int:
        """记录安全事件"""
        try:
            sql = """
                INSERT INTO security_logs 
                (userid, session_id, event_type, severity, content, detected_keywords, 
                 action_taken, ip_address, user_agent, metadata)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            detected_keywords_json = json.dumps(detected_keywords) if detected_keywords else None
            metadata_json = json.dumps(metadata) if metadata else None
            
            return self.db.execute_insert(
                sql, 
                (userid, session_id, event_type, severity, content, detected_keywords_json,
                 action_taken, ip_address, user_agent, metadata_json),
                user_id=userid,
                context="log_security_event"
            )
        except Exception as e:
            logger.error(f"记录安全事件失败: {e}")
            return None
    
    def get_user_security_status(self, userid: str) -> Dict:
        """获取用户安全状态"""
        try:
            sql = """
                SELECT * FROM user_security_status WHERE userid = %s
            """
            result = self.db.execute_query(sql, (userid,), user_id=userid, context="get_user_security_status")
            return result[0] if result else None
        except Exception as e:
            logger.error(f"获取用户安全状态失败: {e}")
            return None
    
    def update_user_security_status(self, userid: str, suspicious_score: int = None,
                                  warning_count: int = None, is_blocked: bool = None,
                                  block_until: time = None, violation_count: int = None) -> bool:
        """更新用户安全状态"""
        try:
            # 先检查用户安全状态是否存在
            existing = self.get_user_security_status(userid)
            
            if existing:
                # 更新现有记录
                updates = []
                params = []
                
                if suspicious_score is not None:
                    updates.append("suspicious_score = %s")
                    params.append(suspicious_score)
                if warning_count is not None:
                    updates.append("warning_count = %s")
                    params.append(warning_count)
                if is_blocked is not None:
                    updates.append("is_blocked = %s")
                    params.append(1 if is_blocked else 0)
                if block_until is not None:
                    updates.append("block_until = %s")
                    params.append(block_until)
                if violation_count is not None:
                    updates.append("violation_count = %s")
                    params.append(violation_count)
                
                if updates:
                    updates.append("last_violation = CURRENT_TIMESTAMP")
                    sql = f"UPDATE user_security_status SET {', '.join(updates)} WHERE userid = %s"
                    params.append(userid)
                    self.db.execute_update(sql, tuple(params), user_id=userid, context="update_user_security_status")
            else:
                # 创建新记录
                sql = """
                    INSERT INTO user_security_status 
                    (userid, suspicious_score, warning_count, is_blocked, block_until, violation_count)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """
                self.db.execute_insert(
                    sql, 
                    (userid, suspicious_score or 0, warning_count or 0, 
                     1 if is_blocked else 0, block_until, violation_count or 0),
                    user_id=userid,
                    context="create_user_security_status"
                )
            
            return True
        except Exception as e:
            logger.error(f"更新用户安全状态失败: {e}")
            return False
    
    def add_keyword(self, keyword: str, category_name: str, severity: str = 'medium',
                   is_regex: bool = False, description: str = None, created_by: str = None) -> int:
        """添加敏感词"""
        try:
            # 获取分类ID
            category_sql = "SELECT id FROM security_keyword_categories WHERE name = %s"
            category_result = self.db.execute_query(category_sql, (category_name,), context="get_keyword_category")
            
            if not category_result:
                raise Exception(f"敏感词分类不存在: {category_name}")
            
            category_id = category_result[0]['id']
            
            sql = """
                INSERT INTO security_keywords 
                (keyword, category_id, severity, is_regex, description, created_by)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            return self.db.execute_insert(
                sql, 
                (keyword, category_id, severity, 1 if is_regex else 0, description, created_by),
                context="add_keyword"
            )
        except Exception as e:
            logger.error(f"添加敏感词失败: {e}")
            return None
    
    def remove_keyword(self, keyword_id: int) -> bool:
        """删除敏感词（软删除，设置为不活跃）"""
        try:
            sql = "UPDATE security_keywords SET is_active = 0 WHERE id = %s"
            affected_rows = self.db.execute_update(sql, (keyword_id,), context="remove_keyword")
            return affected_rows > 0
        except Exception as e:
            logger.error(f"删除敏感词失败: {e}")
            return False

    def get_categories(self) -> List[Dict]:
        """获取所有安全分类"""
        try:
            sql = """
                SELECT id, name, description, severity, is_active, created_at, updated_at
                FROM security_keyword_categories 
                WHERE is_active = 1
                ORDER BY name ASC
            """
            return self.db.execute_query(sql, context="get_categories")
        except Exception as e:
            logger.error(f"获取安全分类失败: {e}")
            return []

    def get_all_keywords(self) -> List[Dict]:
        """获取所有敏感词（包括禁用的）"""
        try:
            sql = """
                SELECT k.*, c.name as category_name
                FROM security_keywords k
                JOIN security_keyword_categories c ON k.category_id = c.id
                ORDER BY k.created_at DESC
            """
            return self.db.execute_query(sql, context="get_all_keywords")
        except Exception as e:
            logger.error(f"获取所有敏感词失败: {e}")
            return []

    def get_keywords_statistics(self) -> Dict:
        """获取敏感词统计信息"""
        try:
            # 总数
            total_sql = "SELECT COUNT(*) as count FROM security_keywords"
            total_result = self.db.execute_query(total_sql, context="get_total_keywords")
            total = total_result[0]['count'] if total_result else 0

            # 启用数
            active_sql = "SELECT COUNT(*) as count FROM security_keywords WHERE is_active = 1"
            active_result = self.db.execute_query(active_sql, context="get_active_keywords_count")
            active = active_result[0]['count'] if active_result else 0

            # 高危险级别数
            high_sql = "SELECT COUNT(*) as count FROM security_keywords WHERE severity = 'high' AND is_active = 1"
            high_result = self.db.execute_query(high_sql, context="get_high_severity_keywords")
            high_severity = high_result[0]['count'] if high_result else 0

            return {
                'total': total,
                'active': active,
                'high_severity': high_severity
            }
        except Exception as e:
            logger.error(f"获取敏感词统计失败: {e}")
            return {'total': 0, 'active': 0, 'high_severity': 0}

    def add_keyword_by_category_id(self, keyword: str, category_id: int, severity: str = 'medium',
                                  is_regex: bool = False, description: str = None, created_by: str = None) -> int:
        """通过分类ID添加敏感词"""
        try:
            sql = """
                INSERT INTO security_keywords 
                (keyword, category_id, severity, is_regex, description, created_by)
                VALUES (%s, %s, %s, %s, %s, %s)
            """
            return self.db.execute_insert(
                sql, 
                (keyword, category_id, severity, 1 if is_regex else 0, description, created_by),
                context="add_keyword_by_category_id"
            )
        except Exception as e:
            logger.error(f"添加敏感词失败: {e}")
            return None

    def update_keyword(self, keyword_id: int, keyword: str = None, category_id: int = None,
                      severity: str = None, is_regex: bool = None, is_active: bool = None,
                      description: str = None) -> bool:
        """更新敏感词"""
        try:
            updates = []
            params = []
            
            if keyword is not None:
                updates.append("keyword = %s")
                params.append(keyword)
            if category_id is not None:
                updates.append("category_id = %s")
                params.append(category_id)
            if severity is not None:
                updates.append("severity = %s")
                params.append(severity)
            if is_regex is not None:
                updates.append("is_regex = %s")
                params.append(1 if is_regex else 0)
            if is_active is not None:
                updates.append("is_active = %s")
                params.append(1 if is_active else 0)
            if description is not None:
                updates.append("description = %s")
                params.append(description)
            
            if not updates:
                return False
            
            sql = f"UPDATE security_keywords SET {', '.join(updates)} WHERE id = %s"
            params.append(keyword_id)
            
            affected_rows = self.db.execute_update(sql, tuple(params), context="update_keyword")
            return affected_rows > 0
        except Exception as e:
            logger.error(f"更新敏感词失败: {e}")
            return False

    def delete_keyword(self, keyword_id: int) -> bool:
        """删除敏感词（物理删除）"""
        try:
            sql = "DELETE FROM security_keywords WHERE id = %s"
            affected_rows = self.db.execute_update(sql, (keyword_id,), context="delete_keyword")
            return affected_rows > 0
        except Exception as e:
            logger.error(f"删除敏感词失败: {e}")
            return False

    def toggle_keyword_status(self, keyword_id: int) -> bool:
        """切换敏感词状态"""
        try:
            sql = "UPDATE security_keywords SET is_active = 1 - is_active WHERE id = %s"
            affected_rows = self.db.execute_update(sql, (keyword_id,), context="toggle_keyword_status")
            return affected_rows > 0
        except Exception as e:
            logger.error(f"切换敏感词状态失败: {e}")
            return False

    def batch_delete_keywords(self, keyword_ids: List[int]) -> int:
        """批量删除敏感词"""
        try:
            if not keyword_ids:
                return 0
            
            placeholders = ','.join(['%s'] * len(keyword_ids))
            sql = f"DELETE FROM security_keywords WHERE id IN ({placeholders})"
            affected_rows = self.db.execute_update(sql, tuple(keyword_ids), context="batch_delete_keywords")
            return affected_rows
        except Exception as e:
            logger.error(f"批量删除敏感词失败: {e}")
            return 0

    def batch_toggle_keywords(self, keyword_ids: List[int]) -> int:
        """批量切换敏感词状态"""
        try:
            if not keyword_ids:
                return 0
            
            placeholders = ','.join(['%s'] * len(keyword_ids))
            sql = f"UPDATE security_keywords SET is_active = 1 - is_active WHERE id IN ({placeholders})"
            affected_rows = self.db.execute_update(sql, tuple(keyword_ids), context="batch_toggle_keywords")
            return affected_rows
        except Exception as e:
            logger.error(f"批量切换敏感词状态失败: {e}")
            return 0

# 全局数据库实例
_chat_db = None

def init_chat_database():
    """初始化聊天数据库"""
    global _chat_db
    try:
        _chat_db = ChatDatabase()
        success = _chat_db.init_database()
        if success:
            logger.info("✅ MySQL数据库初始化成功")
        return success
    except Exception as e:
        logger.error(f"❌ MySQL数据库初始化失败: {e}")
        _chat_db = None
        return False

def get_chat_db():
    """获取聊天数据库实例"""
    global _chat_db
    if _chat_db is None:
        init_chat_database()
    return _chat_db 