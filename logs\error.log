2025-06-30 09:35:42 - error - ERROR - logger_config.py:222 - log_error - 除零错误示例
Traceback (most recent call last):
  File "E:\workspace\cnpcklwc0627\logging_example.py", line 141, in example_error_logging
    result = 1 / 0
             ~~^~~
ZeroDivisionError: division by zero
2025-06-30 09:35:42 - error - ERROR - logger_config.py:222 - log_error - 用户权限不足
2025-06-30 10:04:45 - error - ERROR - logger_config.py:222 - log_error - 除零错误示例
Traceback (most recent call last):
  File "E:\workspace\cnpcklwc0627\logging_example.py", line 141, in example_error_logging
    result = 1 / 0
             ~~^~~
ZeroDivisionError: division by zero
2025-06-30 10:04:45 - error - ERROR - logger_config.py:222 - log_error - 用户权限不足
2025-06-30 10:18:16 - error - ERROR - logger_config.py:222 - log_error - 除零错误示例
Traceback (most recent call last):
  File "E:\workspace\cnpcklwc0627\logging_example.py", line 141, in example_error_logging
    result = 1 / 0
             ~~^~~
ZeroDivisionError: division by zero
2025-06-30 10:18:16 - error - ERROR - logger_config.py:222 - log_error - 用户权限不足
2025-07-02 08:39:10 - error - ERROR - logger_config.py:222 - log_error - 人大金仓数据库连接测试失败: connection to server at "***************", port 54321 failed: Connection timed out (0x0000274C/10060)
	Is the server running on that host and accepting TCP/IP connections?
Traceback (most recent call last):
  File "E:\test\cnpcklwc0627\database.py", line 91, in test_connection
    connection = psycopg2.connect(**DB_CONFIG)
  File "C:\Python313\Lib\site-packages\psycopg2\__init__.py", line 135, in connect
    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
psycopg2.OperationalError: connection to server at "***************", port 54321 failed: Connection timed out (0x0000274C/10060)
	Is the server running on that host and accepting TCP/IP connections?

2025-07-02 08:39:41 - error - ERROR - logger_config.py:222 - log_error - 人大金仓数据库连接测试失败: connection to server at "***************", port 54321 failed: Connection timed out (0x0000274C/10060)
	Is the server running on that host and accepting TCP/IP connections?
Traceback (most recent call last):
  File "E:\test\cnpcklwc0627\database.py", line 91, in test_connection
    connection = psycopg2.connect(**DB_CONFIG)
  File "C:\Python313\Lib\site-packages\psycopg2\__init__.py", line 135, in connect
    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
psycopg2.OperationalError: connection to server at "***************", port 54321 failed: Connection timed out (0x0000274C/10060)
	Is the server running on that host and accepting TCP/IP connections?

2025-07-04 10:02:59 - error - ERROR - logger_config.py:222 - log_error - 人大金仓数据库连接测试失败: connection to server at "***************", port 54321 failed: Connection timed out (0x0000274C/10060)
	Is the server running on that host and accepting TCP/IP connections?
Traceback (most recent call last):
  File "E:\test\cnpcklwc0627\database.py", line 91, in test_connection
    connection = psycopg2.connect(**DB_CONFIG)
  File "C:\Python313\Lib\site-packages\psycopg2\__init__.py", line 135, in connect
    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
psycopg2.OperationalError: connection to server at "***************", port 54321 failed: Connection timed out (0x0000274C/10060)
	Is the server running on that host and accepting TCP/IP connections?

2025-07-04 10:03:59 - error - ERROR - logger_config.py:222 - log_error - 人大金仓数据库连接测试失败: connection to server at "***************", port 54321 failed: Connection timed out (0x0000274C/10060)
	Is the server running on that host and accepting TCP/IP connections?
Traceback (most recent call last):
  File "E:\test\cnpcklwc0627\database.py", line 91, in test_connection
    connection = psycopg2.connect(**DB_CONFIG)
  File "C:\Python313\Lib\site-packages\psycopg2\__init__.py", line 135, in connect
    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
psycopg2.OperationalError: connection to server at "***************", port 54321 failed: Connection timed out (0x0000274C/10060)
	Is the server running on that host and accepting TCP/IP connections?

2025-07-11 20:22:07 - error - ERROR - logger_config.py:222 - log_error - API请求异常: HTTPConnectionPool(host='invalid-url-for-testing', port=80): Max retries exceeded with url: /v1/chat/completions (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x0000023B6FBF4EC0>: Failed to resolve 'invalid-url-for-testing' ([Errno 11001] getaddrinfo failed)"))
2025-07-11 20:22:33 - error - ERROR - logger_config.py:222 - log_error - API请求异常: HTTPConnectionPool(host='invalid-url-for-testing', port=80): Max retries exceeded with url: /v1/chat/completions (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x000001E69D5B4EC0>: Failed to resolve 'invalid-url-for-testing' ([Errno 11001] getaddrinfo failed)"))
2025-07-11 20:22:59 - error - ERROR - logger_config.py:222 - log_error - API请求异常: HTTPConnectionPool(host='invalid-url-for-testing', port=80): Max retries exceeded with url: /v1/chat/completions (Caused by NameResolutionError("<urllib3.connection.HTTPConnection object at 0x000001B434D54EC0>: Failed to resolve 'invalid-url-for-testing' ([Errno 11001] getaddrinfo failed)"))
