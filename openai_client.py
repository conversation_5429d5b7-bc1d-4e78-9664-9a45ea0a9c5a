#!/usr/bin/env python3
"""
OpenAI兼容接口客户端
支持标准OpenAI API格式的聊天接口调用
"""

import os
import json
import time
import requests
import logging
from typing import List, Dict, Any, Optional, Generator, Union
from dotenv import load_dotenv

# 导入日志模块
from logger_config import log_api_call, log_error

# 加载环境变量
load_dotenv()

# 配置日志
logger = logging.getLogger(__name__)

class OpenAICompatibleClient:
    """OpenAI兼容接口客户端"""
    
    def __init__(self, 
                 api_key: str = None, 
                 base_url: str = None, 
                 model: str = None,
                 timeout: int = 60):
        """
        初始化OpenAI兼容客户端
        
        Args:
            api_key: API密钥
            base_url: API基础URL
            model: 默认模型名称
            timeout: 请求超时时间（秒）
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY", "")
        self.base_url = base_url or os.getenv("OPENAI_API_URL", "https://api.openai.com/v1")
        self.model = model or os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")
        self.timeout = timeout
        
        # 确保base_url以/v1结尾，但不重复添加
        if self.base_url.endswith('/v1/'):
            self.base_url = self.base_url.rstrip('/')
        elif not self.base_url.endswith('/v1'):
            if self.base_url.endswith('/'):
                self.base_url = self.base_url.rstrip('/') + '/v1'
            else:
                self.base_url += '/v1'
        
        # 构建完整的聊天接口URL
        self.chat_url = f"{self.base_url}/chat/completions"
        
        logger.info(f"OpenAI兼容客户端初始化完成 - URL: {self.chat_url}, Model: {self.model}")
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        headers = {
            "Content-Type": "application/json; charset=utf-8",
            "Accept": "application/json, text/event-stream",
            "Accept-Charset": "utf-8",
        }

        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"

        return headers
    
    def _convert_history_to_messages(self, history: List[Dict]) -> List[Dict]:
        """
        将内部history格式转换为OpenAI标准messages格式
        
        Args:
            history: 内部历史消息格式 [{"role": "user", "content": "..."}, ...]
            
        Returns:
            OpenAI标准消息格式
        """
        messages = []
        
        for msg in history:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            
            # 确保role是OpenAI支持的角色
            if role not in ["system", "user", "assistant"]:
                role = "user"
            
            # 构建标准消息格式
            message = {
                "role": role,
                "content": content
            }
            
            messages.append(message)
        
        return messages
    
    def chat_completion(self, 
                       messages: List[Dict] = None,
                       history: List[Dict] = None,
                       model: str = None,
                       temperature: float = 0.7,
                       max_tokens: int = 2000,
                       stream: bool = False,
                       **kwargs) -> Union[Dict, Generator]:
        """
        调用OpenAI兼容的聊天接口
        
        Args:
            messages: OpenAI标准消息格式
            history: 内部历史消息格式（会自动转换）
            model: 模型名称
            temperature: 温度参数
            max_tokens: 最大token数
            stream: 是否使用流式响应
            **kwargs: 其他参数
            
        Returns:
            非流式：返回完整响应字典
            流式：返回生成器
        """
        api_start_time = time.time()
        
        # 处理消息格式
        if messages is None and history is not None:
            messages = self._convert_history_to_messages(history)
        elif messages is None:
            raise ValueError("必须提供messages或history参数")
        
        # 构建请求数据
        request_data = {
            "model": model or self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": stream,
            **kwargs
        }
        
        headers = self._get_headers()
        
        try:
            response = requests.post(
                self.chat_url,
                headers=headers,
                json=request_data,
                timeout=self.timeout,
                stream=stream
            )

            # 确保响应使用UTF-8编码
            response.encoding = 'utf-8'
            
            api_duration = time.time() - api_start_time
            
            if response.status_code != 200:
                error_msg = f"API调用失败: {response.status_code} - {response.text}"
                log_error(error_msg)
                raise Exception(error_msg)
            
            if stream:
                return self._handle_stream_response(response, api_duration)
            else:
                result = response.json()
                
                # 记录API调用日志
                log_api_call(
                    endpoint=self.chat_url,
                    method="POST",
                    duration=api_duration,
                    status_code=response.status_code,
                    response_length=len(json.dumps(result))
                )
                
                return result
                
        except requests.exceptions.Timeout:
            error_msg = "API请求超时"
            log_error(error_msg)
            raise Exception(error_msg)
        except requests.exceptions.RequestException as e:
            error_msg = f"API请求异常: {str(e)}"
            log_error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"OpenAI API调用异常: {str(e)}"
            log_error(error_msg)
            raise Exception(error_msg)
    
    def _handle_stream_response(self, response, api_duration: float) -> Generator:
        """
        处理流式响应
        
        Args:
            response: requests响应对象
            api_duration: API调用耗时
            
        Yields:
            流式响应数据
        """
        try:
            full_content = ""

            for line in response.iter_lines(decode_unicode=True, chunk_size=8192):
                if not line:
                    continue

                # OpenAI流式响应格式：data: {...}
                if line.startswith("data: "):
                    data_str = line[6:]  # 移除"data: "前缀
                    
                    # 检查是否是结束标记
                    if data_str.strip() == "[DONE]":
                        break
                    
                    try:
                        chunk_data = json.loads(data_str)
                        
                        # 提取内容
                        choices = chunk_data.get("choices", [])
                        if choices:
                            delta = choices[0].get("delta", {})
                            content = delta.get("content", "")
                            
                            if content:
                                full_content += content
                                yield {
                                    "type": "token",
                                    "data": content,
                                    "raw_chunk": chunk_data
                                }
                            
                            # 检查是否结束
                            finish_reason = choices[0].get("finish_reason")
                            if finish_reason:
                                yield {
                                    "type": "finish",
                                    "data": {
                                        "finish_reason": finish_reason,
                                        "full_content": full_content
                                    }
                                }
                                break
                    
                    except json.JSONDecodeError:
                        continue
            
            # 记录API调用日志
            log_api_call(
                endpoint=self.chat_url,
                method="POST",
                duration=api_duration,
                status_code=response.status_code,
                response_length=len(full_content)
            )
            
        except Exception as e:
            error_msg = f"处理流式响应异常: {str(e)}"
            log_error(error_msg)
            yield {
                "type": "error",
                "data": error_msg
            }
    
    def get_simple_response(self, 
                           message: str, 
                           history: List[Dict] = None,
                           **kwargs) -> str:
        """
        获取简单的文本响应（非流式）
        
        Args:
            message: 用户消息
            history: 历史对话
            **kwargs: 其他参数
            
        Returns:
            AI响应文本
        """
        # 构建消息列表
        messages = []
        
        if history:
            messages = self._convert_history_to_messages(history)
        
        messages.append({"role": "user", "content": message})
        
        try:
            result = self.chat_completion(
                messages=messages,
                stream=False,
                **kwargs
            )
            
            # 提取响应内容
            choices = result.get("choices", [])
            if choices:
                return choices[0].get("message", {}).get("content", "")
            else:
                return "未收到有效响应"
                
        except Exception as e:
            return f"获取响应失败: {str(e)}"


# 全局客户端实例
_openai_client = None

def get_openai_client() -> OpenAICompatibleClient:
    """获取全局OpenAI客户端实例"""
    global _openai_client
    if _openai_client is None:
        _openai_client = OpenAICompatibleClient()
    return _openai_client

def init_openai_client(api_key: str = None, 
                      base_url: str = None, 
                      model: str = None) -> OpenAICompatibleClient:
    """初始化全局OpenAI客户端"""
    global _openai_client
    _openai_client = OpenAICompatibleClient(
        api_key=api_key,
        base_url=base_url,
        model=model
    )
    return _openai_client
