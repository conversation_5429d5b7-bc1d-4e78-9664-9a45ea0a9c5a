#!/usr/bin/env python3
"""
编码修复测试脚本
用于验证中文字符编码是否正确
"""

import os
import json
import requests
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_encoding_fix():
    """测试编码修复"""
    print("🔍 测试中文编码修复...")
    
    api_key = os.getenv('OPENAI_API_KEY')
    api_url = os.getenv('OPENAI_API_URL')
    model = os.getenv('OPENAI_MODEL')
    
    if not all([api_key, api_url, model]):
        print("❌ 配置不完整")
        return False
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json; charset=utf-8",
        "Accept": "application/json, text/event-stream",
        "Accept-Charset": "utf-8",
    }
    
    data = {
        "model": model,
        "messages": [
            {"role": "user", "content": "请用中文回复：你好，今天天气怎么样？"}
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }
    
    try:
        chat_url = f"{api_url}/chat/completions"
        print(f"请求URL: {chat_url}")
        
        response = requests.post(
            chat_url,
            headers=headers,
            json=data,
            timeout=30
        )
        
        # 确保响应使用UTF-8编码
        response.encoding = 'utf-8'
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应编码: {response.encoding}")
        print(f"响应头Content-Type: {response.headers.get('Content-Type', 'N/A')}")
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ 非流式API调用成功!")
            print(f"响应内容: {content}")
            
            # 检查是否包含中文字符
            if any('\u4e00' <= char <= '\u9fff' for char in content):
                print("✅ 中文字符显示正常")
                return True
            else:
                print("⚠️ 响应中没有中文字符或显示异常")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_streaming_encoding():
    """测试流式响应编码"""
    print("\n🔍 测试流式响应中文编码...")
    
    api_key = os.getenv('OPENAI_API_KEY')
    api_url = os.getenv('OPENAI_API_URL')
    model = os.getenv('OPENAI_MODEL')
    
    if not all([api_key, api_url, model]):
        print("❌ 配置不完整")
        return False
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json; charset=utf-8",
        "Accept": "text/event-stream",
        "Accept-Charset": "utf-8",
    }
    
    data = {
        "model": model,
        "messages": [
            {"role": "user", "content": "请写一首关于春天的中文诗，要包含花朵、阳光等词汇"}
        ],
        "max_tokens": 200,
        "temperature": 0.7,
        "stream": True
    }
    
    try:
        chat_url = f"{api_url}/chat/completions"
        
        response = requests.post(
            chat_url,
            headers=headers,
            json=data,
            timeout=30,
            stream=True
        )
        
        # 确保响应使用UTF-8编码
        response.encoding = 'utf-8'
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应编码: {response.encoding}")
        
        if response.status_code == 200:
            print("✅ 流式API调用成功!")
            print("流式响应内容:")
            
            full_content = ""
            for line in response.iter_lines(decode_unicode=True, chunk_size=8192):
                if not line:
                    continue
                
                if line.startswith("data: "):
                    data_str = line[6:]
                    if data_str.strip() == "[DONE]":
                        break
                    
                    try:
                        chunk_data = json.loads(data_str)
                        choices = chunk_data.get("choices", [])
                        if choices:
                            delta = choices[0].get("delta", {})
                            content = delta.get("content", "")
                            if content:
                                print(content, end="", flush=True)
                                full_content += content
                    except json.JSONDecodeError:
                        continue
            
            print(f"\n\n完整响应长度: {len(full_content)} 字符")
            
            # 检查是否包含中文字符
            chinese_chars = [char for char in full_content if '\u4e00' <= char <= '\u9fff']
            if chinese_chars:
                print(f"✅ 中文字符显示正常，共 {len(chinese_chars)} 个中文字符")
                print(f"示例中文字符: {chinese_chars[:10]}")
                return True
            else:
                print("⚠️ 响应中没有中文字符或显示异常")
                print(f"原始内容（前100字符）: {repr(full_content[:100])}")
                return False
        else:
            print(f"❌ 流式API调用失败: {response.status_code}")
            print(f"错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 流式测试异常: {e}")
        return False

def test_openai_client():
    """测试修复后的OpenAI客户端"""
    print("\n🔍 测试修复后的OpenAI客户端...")
    
    try:
        from openai_client import get_openai_client
        
        client = get_openai_client()
        
        # 测试简单响应
        response = client.get_simple_response(
            "请用中文简单介绍一下春天的特点，不超过50字",
            timeout=30
        )
        
        print(f"客户端响应: {response}")
        
        # 检查是否包含中文字符
        if any('\u4e00' <= char <= '\u9fff' for char in response):
            print("✅ OpenAI客户端中文显示正常")
            return True
        else:
            print("⚠️ OpenAI客户端中文显示异常")
            print(f"原始响应: {repr(response)}")
            return False
            
    except Exception as e:
        print(f"❌ OpenAI客户端测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始中文编码修复测试")
    print("=" * 60)
    
    tests = [
        ("非流式API编码", test_encoding_fix),
        ("流式API编码", test_streaming_encoding),
        ("OpenAI客户端编码", test_openai_client),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "="*60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有编码测试通过！中文显示正常！")
        return True
    else:
        print("❌ 部分编码测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
