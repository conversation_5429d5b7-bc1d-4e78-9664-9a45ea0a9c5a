import os
import time
import json
import requests
import uuid
import jwt
import hashlib
import ipaddress
from datetime import datetime, timedelta
from functools import wraps
from flask import Flask, render_template, request, jsonify, session, redirect, url_for
from flask_socketio import SocketIO
from database import init_chat_database, get_chat_db

# 导入日志模块
from logger_config import (
    log_app_event, log_api_call, log_user_operation, 
    log_error, log_performance, log_function_call
)

# 导入缓存配置
from cache_config import cache_manager, get_cache_stats

# 导入OpenAI兼容客户端
from openai_client import get_openai_client, init_openai_client

app = Flask(__name__)
app.config["SECRET_KEY"] = os.getenv("FLASK_SECRET_KEY", "your-super-secret-key-change-this")
socketio = SocketIO(app, async_mode="threading")

# 配置信息
CONFIG = {
    "model": os.getenv("OPENAI_MODEL", "gpt-3.5-turbo"),
    "api_key": os.getenv("OPENAI_API_KEY", ""),
    "api_url": os.getenv("OPENAI_API_URL", "https://api.openai.com/v1"),
    "temperature": float(os.getenv("MODEL_TEMPERATURE", "0.7")),
    "max_tokens": int(os.getenv("MODEL_MAX_TOKENS", "2000")),
    "history_limit": 10,
    "use_database": os.getenv("USE_DATABASE", "true").lower() == "true",
}

# 安全配置
SECURITY_CONFIG = {
    "jwt_secret": os.getenv("JWT_SECRET", "your-jwt-secret-key-change-this"),
    "jwt_expiration": int(os.getenv("JWT_EXPIRATION", "3600")),  # 1小时
    "admin_token_expiration": int(os.getenv("ADMIN_TOKEN_EXPIRATION", "1800")),  # 30分钟
    "max_login_attempts": int(os.getenv("MAX_LOGIN_ATTEMPTS", "5")),
    "lockout_duration": int(os.getenv("LOCKOUT_DURATION", "900")),  # 15分钟
    "admin_ip_whitelist": os.getenv("ADMIN_IP_WHITELIST", "127.0.0.1,::1").split(","),
    "enable_2fa": os.getenv("ENABLE_2FA", "false").lower() == "true",
}

# 用户会话历史存储（作为数据库的备用方案）
# 现在主要使用Redis缓存，这里保留作为降级方案
user_sessions = {}

# 管理员用户配置（从环境变量获取）
ADMIN_USERS = {}
admin_users_str = os.getenv("ADMIN_USERS", "")
for user_config in admin_users_str.split(","):
    if ":" in user_config:
        username, password = user_config.split(":", 1)
        ADMIN_USERS[username] = {
            "password_hash": hashlib.sha256(password.encode()).hexdigest(),
            "role": "admin"
        }

# 登录尝试记录
login_attempts = {}

# 活跃的管理员会话
active_admin_sessions = {}

# 初始化数据库
try:
    if CONFIG["use_database"]:
        db_success = init_chat_database()
        if db_success:
            log_app_event("MySQL数据库连接成功", level='info')

            # 记录数据库连接池状态
            chat_db = get_chat_db()
            pool_stats = chat_db.db_manager.get_pool_stats()
            log_app_event(f"数据库连接池状态: {pool_stats}", level='info')
        else:
            log_app_event("MySQL数据库连接失败，切换到内存存储", level='warning')
            CONFIG["use_database"] = False
    else:
        log_app_event("使用内存存储模式", level='info')
except Exception as e:
    log_error(f"数据库初始化异常: {e}", exc_info=True)
    CONFIG["use_database"] = False

# 初始化OpenAI兼容客户端
try:
    openai_client = init_openai_client(
        api_key=CONFIG["api_key"],
        base_url=CONFIG["api_url"],
        model=CONFIG["model"]
    )
    log_app_event(f"OpenAI兼容客户端初始化成功 - Model: {CONFIG['model']}, URL: {CONFIG['api_url']}", level='info')
except Exception as e:
    log_app_event(f"OpenAI兼容客户端初始化失败: {e}", level='error')

# 初始化缓存
try:
    if cache_manager.is_available():
        cache_stats = get_cache_stats()
        log_app_event(f"Redis缓存连接成功: {cache_stats}", level='info')
    else:
        log_app_event("Redis缓存连接失败，使用内存存储", level='warning')
except Exception as e:
    log_error(f"缓存初始化异常: {e}", exc_info=True)

def get_client_ip():
    """获取客户端真实IP"""
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr

def is_ip_whitelisted(ip):
    """检查IP是否在白名单中"""
    try:
        client_ip = ipaddress.ip_address(ip)
        for allowed_ip in SECURITY_CONFIG["admin_ip_whitelist"]:
            if ipaddress.ip_address(allowed_ip.strip()) == client_ip:
                return True
        return False
    except:
        return False

def generate_admin_token(userid, ip_address):
    """生成管理员访问令牌"""
    payload = {
        'userid': userid,
        'role': 'admin',
        'ip': ip_address,
        'iat': datetime.utcnow(),
        'exp': datetime.utcnow() + timedelta(seconds=SECURITY_CONFIG["admin_token_expiration"])
    }
    return jwt.encode(payload, SECURITY_CONFIG["jwt_secret"], algorithm='HS256')

def verify_admin_token(token):
    """验证管理员令牌"""
    try:
        payload = jwt.decode(token, SECURITY_CONFIG["jwt_secret"], algorithms=['HS256'])
        
        # 检查IP是否匹配
        if payload.get('ip') != get_client_ip():
            return None
            
        # 检查是否是管理员
        if payload.get('role') != 'admin':
            return None
            
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

def check_login_attempts(ip_address):
    """检查登录尝试次数"""
    current_time = time.time()
    
    if ip_address not in login_attempts:
        login_attempts[ip_address] = []
    
    # 清理过期的尝试记录
    login_attempts[ip_address] = [
        attempt for attempt in login_attempts[ip_address]
        if current_time - attempt < SECURITY_CONFIG["lockout_duration"]
    ]
    
    return len(login_attempts[ip_address]) < SECURITY_CONFIG["max_login_attempts"]

def record_login_attempt(ip_address, success=False):
    """记录登录尝试"""
    if not success:
        if ip_address not in login_attempts:
            login_attempts[ip_address] = []
        login_attempts[ip_address].append(time.time())
    else:
        # 成功登录后清除尝试记录
        if ip_address in login_attempts:
            del login_attempts[ip_address]

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        client_ip = get_client_ip()

        # 检查IP白名单
        if not is_ip_whitelisted(client_ip):
            log_error(f"未授权IP尝试访问管理功能: {client_ip}")
            return jsonify({"error": "访问被拒绝：IP地址未授权"}), 403

        # 检查令牌
        token = request.headers.get('Authorization')
        if not token or not token.startswith('Bearer '):
            token = request.args.get('admin_token')
            if not token:
                return jsonify({"error": "访问被拒绝：缺少管理员令牌"}), 401
        else:
            token = token.split(' ')[1]

        payload = verify_admin_token(token)
        if not payload:
            return jsonify({"error": "访问被拒绝：令牌无效或已过期"}), 401

        # 将用户信息添加到request对象中，供被装饰的函数使用
        request.current_user = payload

        # 记录管理员操作
        log_user_operation(
            f"管理员操作: {request.endpoint}",
            payload['userid'],
            session_id=None,
            category='admin',
            details=f"IP: {client_ip}, 路径: {request.path}"
        )

        return f(*args, **kwargs)
    return decorated_function

def is_admin_user(userid):
    """检查用户是否为管理员（保留兼容性）"""
    if not userid:
        return False
    
    # 检查是否在管理员列表中
    if userid in ADMIN_USERS:
        return True
    
    # 如果启用数据库，可以从数据库中检查用户角色
    if CONFIG["use_database"]:
        try:
            chat_db = get_chat_db()
            user_data = chat_db.user_manager.get_user(userid)
            if user_data and user_data.get('role') == 'admin':
                return True
        except Exception as e:
            log_error(f"检查用户权限失败: {e}", exc_info=True)
    
    return False

# =====================================
# 会话缓存管理函数
# =====================================

def get_session_from_cache(userid: str, session_id: str) -> dict:
    """从缓存获取用户会话"""
    cache_key = f"{userid}_{session_id}"
    cached_session = cache_manager.get('user_session', cache_key)
    
    if cached_session:
        return cached_session
    
    # 缓存未命中，返回默认会话结构
    default_session = {
        "user_info": {"name": f"用户_{userid}", "avatar": "User"},
        "history": []
    }
    
    # 存入缓存
    cache_manager.set('user_session', cache_key, default_session)
    
    return default_session

def save_session_to_cache(userid: str, session_id: str, session_data: dict):
    """保存用户会话到缓存"""
    cache_key = f"{userid}_{session_id}"
    cache_manager.set('user_session', cache_key, session_data)

def add_message_to_session_cache(userid: str, session_id: str, message: dict):
    """向会话缓存添加消息"""
    session_data = get_session_from_cache(userid, session_id)
    session_data["history"].append(message)
    save_session_to_cache(userid, session_id, session_data)

def clear_session_cache(userid: str, session_id: str = None):
    """清除会话缓存"""
    if session_id:
        cache_key = f"{userid}_{session_id}"
        cache_manager.delete('user_session', cache_key)
    else:
        # 清除用户所有会话缓存
        cache_manager.delete_pattern(f"kunlun:session:{userid}_*")

@app.route("/admin/login", methods=["GET", "POST"])
def admin_login():
    """管理员登录"""
    if request.method == "GET":
        return render_template("admin_login.html")
    
    client_ip = get_client_ip()
    
    # 检查IP白名单
    if not is_ip_whitelisted(client_ip):
        log_error(f"未授权IP尝试登录: {client_ip}")
        return jsonify({"error": "访问被拒绝：IP地址未授权"}), 403
    
    # 检查登录尝试次数
    if not check_login_attempts(client_ip):
        return jsonify({"error": f"登录尝试次数过多，请{SECURITY_CONFIG['lockout_duration']//60}分钟后再试"}), 429
    
    data = request.json
    username = data.get('username', '').strip()
    password = data.get('password', '').strip()
    
    if not username or not password:
        return jsonify({"error": "用户名和密码不能为空"}), 400
    
    # 验证用户名和密码
    if username in ADMIN_USERS:
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        if ADMIN_USERS[username]["password_hash"] == password_hash:
            # 登录成功
            record_login_attempt(client_ip, success=True)
            
            # 生成管理员令牌
            admin_token = generate_admin_token(username, client_ip)
            
            # 记录登录日志
            log_user_operation(
                f"管理员登录成功",
                username,
                session_id=None,
                category='admin_auth',
                details=f"IP: {client_ip}"
            )
            
            return jsonify({
                "success": True,
                "admin_token": admin_token,
                "expires_in": SECURITY_CONFIG["admin_token_expiration"]
            })
    
    # 登录失败
    record_login_attempt(client_ip, success=False)
    log_error(f"管理员登录失败: {username}, IP: {client_ip}")
    
    return jsonify({"error": "用户名或密码错误"}), 401

@app.route("/admin/logout", methods=["POST"])
def admin_logout():
    """管理员退出登录"""
    client_ip = get_client_ip()

    # 获取token信息用于日志记录
    token = request.headers.get('Authorization')
    if token and token.startswith('Bearer '):
        token = token.split(' ')[1]
        payload = verify_admin_token(token)
        if payload:
            userid = payload.get('userid', 'unknown')
            # 记录退出日志
            log_user_operation(
                f"管理员退出登录",
                userid,
                session_id=None,
                category='admin_auth',
                details=f"IP: {client_ip}"
            )

    return jsonify({
        "success": True,
        "message": "退出登录成功"
    })

@app.route("/")
def index():
    """主页面"""
    userid = request.args.get('userid', 'default_user')
    
    # 初始化默认用户信息
    user_info = {
        'username': '昆仑用户',
        'email': '<EMAIL>'
    }
    
    # 如果使用数据库，尝试获取用户信息
    if CONFIG["use_database"]:
        try:
            chat_db = get_chat_db()
            user_data = chat_db.user_manager.get_user(userid)
            if user_data:
                # 使用数据库中的用户信息
                user_info['username'] = user_data.get('username', '昆仑用户')
                user_info['email'] = user_data.get('email', '<EMAIL>')
            else:
                # 用户不存在，创建新用户
                user_info['username'] = f'用户_{userid}'
                user_info['email'] = f'{userid}@kunlun.com'
                chat_db.user_manager.get_or_create_user(userid, user_info['username'])
        except Exception as e:
            log_error(f"获取用户信息失败: {e}", exc_info=True)
            # 使用默认信息
            pass
    
    return render_template("index.html", userid=userid, user_info=user_info)

@app.route("/sensitive-words")
def sensitive_words_page():
    """敏感词管理页面（需要管理员权限）"""
    client_ip = get_client_ip()
    
    # 检查IP白名单
    if not is_ip_whitelisted(client_ip):
        log_error(f"未授权IP尝试访问敏感词管理: {client_ip}")
        return render_template("access_denied.html", 
                             message="访问被拒绝：您的IP地址未被授权访问管理功能。<br><br>请联系系统管理员添加您的IP到白名单。"), 403
    
    # 检查管理员令牌
    admin_token = request.args.get('admin_token')
    if not admin_token:
        # 重定向到登录页面
        return redirect(url_for('admin_login'))
    
    payload = verify_admin_token(admin_token)
    if not payload:
        return render_template("access_denied.html", 
                             message="访问被拒绝：管理员令牌无效或已过期。<br><br>请重新登录获取有效令牌。"), 401
    
    # 记录访问日志
    log_user_operation(
        f"访问敏感词管理页面",
        payload['userid'],
        session_id=None,
        category='admin_access',
        details=f"IP: {client_ip}"
    )
    
    return render_template("sensitive_words.html", 
                         userid=payload['userid'], 
                         admin_token=admin_token)

@app.route("/api/client-ip", methods=["GET"])
def get_client_ip_api():
    """获取客户端IP地址"""
    return jsonify({"ip": get_client_ip()})

@app.route("/api/system/stats", methods=["GET"])
@admin_required
def get_system_stats():
    """获取系统性能统计信息"""
    try:
        # 获取数据库连接池统计
        chat_db = get_chat_db()
        db_stats = chat_db.db_manager.get_pool_stats()
        
        # 获取缓存统计
        cache_stats = get_cache_stats()
        
        # 获取系统资源使用情况
        import psutil
        system_stats = {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent if hasattr(psutil.disk_usage('/'), 'percent') else 0,
            "process_count": len(psutil.pids())
        }
        
        return jsonify({
            "database": db_stats,
            "cache": cache_stats,
            "system": system_stats,
            "timestamp": time.time()
        })
        
    except Exception as e:
        log_error(f"获取系统统计失败: {e}", exc_info=True)
        return jsonify({"error": "获取系统统计失败"}), 500

@app.route("/api/cache/clear", methods=["POST"])
@admin_required
def clear_cache():
    """清空缓存（管理员功能）"""
    try:
        data = request.json
        cache_type = data.get("type", "all")
        
        if cache_type == "all":
            result = cache_manager.clear_all()
            message = "所有缓存已清空" if result else "清空缓存失败"
        else:
            # 清空特定类型的缓存
            pattern = f"kunlun:{cache_type}:*"
            count = cache_manager.delete_pattern(pattern)
            message = f"已清空 {count} 个 {cache_type} 类型的缓存"
        
        return jsonify({
            "success": True,
            "message": message
        })
        
    except Exception as e:
        log_error(f"清空缓存失败: {e}", exc_info=True)
        return jsonify({"error": "清空缓存失败"}), 500


@app.route("/favicon.ico")
def favicon():
    """处理favicon请求"""
    return "", 204


@app.route("/new_chat", methods=["POST"])
def new_chat():
    """创建新对话"""
    data = request.json
    userid = data.get("userid", "default_user")
    old_session_id = data.get("session_id")
    
    # 生成新的session_id
    new_session_id = f"{userid}_{int(time.time())}_{uuid.uuid4().hex[:8]}"
    
    if CONFIG["use_database"]:
        try:
            # 在数据库中创建新会话
            chat_db = get_chat_db()
            chat_db.session_manager.create_session(new_session_id, userid, "新对话")
            return jsonify({"status": "success", "session_id": new_session_id})
        except Exception as e:
            log_error(f"数据库创建会话失败: {e}", exc_info=True)
            # 降级到内存存储
            pass
    
    # 使用缓存存储（降级方案）
    if old_session_id:
        # 尝试从缓存获取旧会话信息
        old_session_data = get_session_from_cache(userid, old_session_id)
        user_info = old_session_data.get("user_info", {"name": f"用户_{userid}", "avatar": "User"})
    else:
        user_info = {"name": f"用户_{userid}", "avatar": "User"}
    
    # 创建新会话缓存
    new_session_data = {"user_info": user_info, "history": []}
    save_session_to_cache(userid, new_session_id, new_session_data)
    
    # 兼容性：同时更新内存存储
    if old_session_id in user_sessions:
        user_info = user_sessions[old_session_id].get("user_info", {})
        user_sessions[new_session_id] = {"user_info": user_info, "history": []}
    else:
        user_sessions[new_session_id] = {
            "user_info": {"name": f"用户_{userid}", "avatar": "User"},
            "history": []
        }
    
    return jsonify({"status": "success", "session_id": new_session_id})


@app.route("/get_history", methods=["POST"])
def get_history():
    """获取历史对话列表"""
    data = request.json
    session_id = data.get("session_id")
    userid = data.get("userid", "default_user")
    
    if CONFIG["use_database"]:
        try:
            # 从数据库获取用户会话列表
            chat_db = get_chat_db()
            sessions = chat_db.session_manager.get_user_sessions(userid, 50)
            
            history_items = []
            
            for session in sessions:
                # 生成会话标题
                title = session.get('title')
                # 如果标题为空或者还是默认的"新对话"，尝试使用第一条用户消息
                if (not title or title == "新对话") and session.get('first_user_message'):
                    title = session['first_user_message'][:20] + "..." if len(session['first_user_message']) > 20 else session['first_user_message']
                elif not title:
                    title = "新对话"
                
                # 处理created_at字段
                created_at = None
                if session.get('created_at'):
                    try:
                        if hasattr(session['created_at'], 'isoformat'):
                            # 是datetime对象
                            created_at = session['created_at'].isoformat()
                        else:
                            # 是字符串，直接使用
                            created_at = str(session['created_at'])
                    except Exception as e:
                        created_at = None

                updated_at = None
                if session.get('updated_at'):
                    try:
                        if hasattr(session['updated_at'], 'isoformat'):
                            # 是datetime对象
                            updated_at = session['updated_at'].isoformat()
                        else:
                            # 是字符串，直接使用
                            updated_at = str(session['updated_at'])
                    except Exception as e:
                        updated_at = None

                history_item = {
                    "id": session['session_id'],
                    "title": title,
                    "created_at":created_at,
                    "active": session['session_id'] == session_id,
                    "message_count": session.get('message_count', 0),
                    "updated_at": updated_at
                }
                
                history_items.append(history_item)
            
            return jsonify(history_items)
            
        except Exception as e:
            log_error(f"数据库获取历史失败: {e}", exc_info=True)
            # 降级到内存存储
            pass
    
    # 使用内存存储
    history_items = generate_history_items(session_id)
    return jsonify(history_items)


@app.route("/get_session_messages", methods=["POST"])
def get_session_messages():
    try:
        data = request.get_json()
        session_id = data.get("session_id")
        userid = data.get("userid", "default_user")

        if not session_id:
            return jsonify({"error": "session_id is required"}), 400

        # 尝试从数据库获取消息
        if CONFIG["use_database"]:
            try:
                db = get_chat_db()
                # 直接获取会话消息
                messages_data = db.message_manager.get_session_messages(session_id)
                
                if messages_data:
                    # 转换时间戳格式并处理metadata
                    messages = []
                    for msg in messages_data:
                        # 处理timestamp字段，可能是created_at字段
                        timestamp = None
                        if msg.get("created_at"):
                            try:
                                # created_at是datetime对象
                                timestamp = int(msg["created_at"].timestamp())
                            except:
                                timestamp = int(time.time())
                        elif msg.get("created_at"):
                            if isinstance(msg.get("created_at"), str):
                                try:
                                    from datetime import datetime
                                    dt = datetime.fromisoformat(msg["created_at"].replace('Z', '+00:00'))
                                    timestamp = int(dt.timestamp())
                                except:
                                    timestamp = int(time.time())
                            else:
                                timestamp = msg.get("created_at", int(time.time()))
                        else:
                            timestamp = int(time.time())

                        # 🔧 构建包含富文本组件的消息对象
                        message_obj = {
                            "role": msg.get("role", "user"),
                            "userid": userid,
                            "content": msg.get("content", ""),
                            "timestamp": msg.get("created_at"),
                            "sequence_num": msg.get("sequence_num", 0),
                            "message_type": msg.get("message_type", "text"),
                            "metadata": msg.get("metadata"),  # 包含表格和图表数据
                        }
                        
                        # 🔧 如果有metadata且是富文本消息，解析组件数据
                        if message_obj["metadata"] and msg.get("message_type") == "rich":
                            metadata = message_obj["metadata"]
                            message_obj["has_tables"] = metadata.get("has_tables", False)
                            message_obj["has_plots"] = metadata.get("has_plots", False)
                            message_obj["table_count"] = metadata.get("table_count", 0)
                            message_obj["plot_count"] = metadata.get("plot_count", 0)
                            
                            # 提取表格和图表数据
                            components = metadata.get("components", {})
                            message_obj["tables"] = components.get("tables", [])
                            message_obj["plots"] = components.get("plots", [])
                        else:
                            # 普通文本消息
                            message_obj["has_tables"] = False
                            message_obj["has_plots"] = False
                            message_obj["table_count"] = 0
                            message_obj["plot_count"] = 0
                            message_obj["tables"] = []
                            message_obj["plots"] = []

                        messages.append(message_obj)
                    
                    return jsonify({
                        "success": True,
                        "history": messages,
                        "source": "database"
                    })
                else:
                    return jsonify({
                        "success": True,
                        "history": [],
                        "source": "database"
                    })
                    
            except Exception as e:
                log_error(f"数据库查询错误: {e}", exc_info=True)
                return jsonify({"error": "Database query failed"}), 500
        else:
            # 如果未使用数据库，返回空历史
            return jsonify({
                "success": True,
                "history": [],
                "source": "memory"
            })

    except Exception as e:
        log_error(f"获取会话消息错误: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500


@app.route("/delete_session", methods=["POST"])
def delete_session():
    """删除会话"""
    try:
        data = request.get_json()
        session_id = data.get("session_id")
        userid = data.get("userid")

        if not session_id or not userid:
            return jsonify({"error": "Session ID and User ID required"}), 400

        if CONFIG["use_database"]:
            try:
                chat_db = get_chat_db()
                chat_db.session_manager.delete_session(session_id, userid)
                return jsonify({"message": "Session deleted successfully"})
            except Exception as e:
                log_error(f"数据库删除会话失败: {e}", exc_info=True)
                return jsonify({"error": f"Database error: {str(e)}"}), 500
        else:
            # 内存模式下删除会话
            session_key = f"{userid}_{session_id}"
            if session_key in user_sessions:
                del user_sessions[session_key]
                return jsonify({"message": "Session deleted successfully"})
            else:
                return jsonify({"error": "Session not found"}), 404

    except Exception as e:
        log_error(f"删除会话异常: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500


@app.route("/rename_session", methods=["POST"])
def rename_session():
    """重命名会话"""
    try:
        data = request.get_json()
        session_id = data.get("session_id")
        userid = data.get("userid")
        new_title = data.get("title")

        if not session_id or not userid or not new_title:
            return jsonify({"error": "Session ID, User ID and title required"}), 400

        # 清理标题，限制长度
        new_title = new_title.strip()
        if len(new_title) > 100:
            new_title = new_title[:100]

        if CONFIG["use_database"]:
            try:
                chat_db = get_chat_db()
                chat_db.session_manager.update_session_title(session_id, new_title)
                return jsonify({"message": "Session renamed successfully", "title": new_title})
            except Exception as e:
                log_error(f"数据库重命名会话失败: {e}", exc_info=True)
                return jsonify({"error": f"Database error: {str(e)}"}), 500
        else:
            # 内存模式下重命名会话
            session_key = f"{userid}_{session_id}"
            if session_key in user_sessions:
                # 内存模式下没有直接的标题字段，这里可以扩展存储结构
                if "title" not in user_sessions[session_key]:
                    user_sessions[session_key]["title"] = new_title
                else:
                    user_sessions[session_key]["title"] = new_title
                return jsonify({"message": "Session renamed successfully", "title": new_title})
            else:
                return jsonify({"error": "Session not found"}), 404

    except Exception as e:
        log_error(f"重命名会话异常: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500


@app.route("/chat", methods=["POST"])
def chat():
    """处理聊天请求"""
    request_start_time = time.time()

    data = request.json
    session_id = data.get("session_id")
    userid = data.get("userid", "default_user")
    message = data.get("message")
    deep_think = data.get("deep_think", False)
    web_search = data.get("web_search", False)

    # 记录用户操作日志
    log_user_operation(
        user_id=userid,
        operation="chat_request",
        session_id=session_id,
        details={
            "message_length": len(message) if message else 0,
            "deep_think": deep_think,
            "web_search": web_search
        }
    )

    if not session_id:
        log_error("聊天请求缺少会话ID", user_id=userid)
        return jsonify({"error": "Session ID required"}), 400

    if not message:
        log_error("聊天请求缺少用户消息", user_id=userid, session_id=session_id)
        return jsonify({"error": "Message required"}), 400

    # =====================================
    # 🛡️ 后端安全检查
    # =====================================
    try:
        security_check = check_message_security_backend(message, userid, session_id)
        

        
        if not security_check["is_safe"] and security_check["should_block"]:
            # 记录安全拦截事件
            log_user_operation(
                user_id=userid,
                operation="chat_blocked_security",
                session_id=session_id,
                details={
                    "reason": security_check["reason"],
                    "severity": security_check["severity"],
                    "violations": security_check.get("violations", [])
                }
            )
            
            return jsonify({
                "error": security_check["reason"],
                "security_violation": True,
                "severity": security_check["severity"],
                "suggestions": [
                    "请使用更自然的语言表达",
                    "避免使用技术指令类词汇",
                    "如有疑问，请联系管理员"
                ]
            }), 403
        
        # 记录安全检查通过
        if not security_check["is_safe"]:
            log_user_operation(
                user_id=userid,
                operation="chat_security_warning",
                session_id=session_id,
                details={
                    "reason": security_check["reason"],
                    "severity": security_check["severity"]
                }
            )
            
    except Exception as security_error:
        log_error(f"安全检查异常: {security_error}", exc_info=True, user_id=userid, session_id=session_id)
        # 安全检查异常时继续处理，但记录警告
        log_user_operation(
            user_id=userid,
            operation="chat_security_error",
            session_id=session_id,
            details={"error": str(security_error)}
        )

    # 数据库存储模式
    if CONFIG["use_database"]:
        try:
            # 保存用户消息到数据库
            chat_db = get_chat_db()
            chat_db.save_chat_message(session_id, userid, "user", message)
            
            # 获取历史消息用于AI推理
            recent_messages = chat_db.message_manager.get_recent_messages(session_id, CONFIG["history_limit"])
            
            # 检查是否是第一条用户消息，如果是则更新会话标题
            user_messages = [msg for msg in recent_messages if msg["role"] == "user"]
            if len(user_messages) == 1:  # 第一条用户消息
                # 生成会话标题（取用户消息的前20个字符）
                title = message[:20] + "..." if len(message) > 20 else message
                try:
                    chat_db.session_manager.update_session_title(session_id, title)
                except Exception as e:
                    log_error(f"更新会话标题失败: {e}", exc_info=True)
            
            # 构建历史对话格式
            history = []
            for msg in recent_messages:
                # 处理timestamp字段
                timestamp = None
                if msg.get("created_at"):
                    try:
                        if hasattr(msg["created_at"], 'timestamp'):
                            # 是datetime对象
                            timestamp = msg["created_at"].timestamp()
                        else:
                            # 是字符串，尝试转换
                            from datetime import datetime
                            dt = datetime.fromisoformat(str(msg["created_at"]).replace('Z', '+00:00'))
                            timestamp = dt.timestamp()
                    except:
                        timestamp = time.time()
                else:
                    timestamp = time.time()
                
                history.append({
                    "role": msg["role"],
                    "content": msg["content"],
                    "timestamp": timestamp
                })
            
            
        except Exception as e:
            log_error(f"数据库操作失败: {e}", exc_info=True)
            # 降级到内存存储（仅当前请求）
            use_database_for_this_request = False
    
    # 内存存储模式（备用方案）
    use_database_for_this_request = CONFIG.get("use_database", True) if 'use_database_for_this_request' not in locals() else use_database_for_this_request
    if not use_database_for_this_request:
        session_key = f"{userid}_{session_id}"
        if session_key not in user_sessions:
            user_sessions[session_key] = {
                "user_info": {"name": f"用户_{userid}", "avatar": "User"},
                "history": [],
            }

        # 添加用户消息到历史
        user_sessions[session_key]["history"].append(
            {"role": "user", "content": message, "timestamp": time.time()}
        )
        history = user_sessions[session_key]["history"]

    try:
        # 使用OpenAI兼容客户端
        openai_client = get_openai_client()
        response = openai_client.get_simple_response(
            message=message,
            history=history[:-1] if history else [],  # 排除刚添加的用户消息
            temperature=CONFIG.get("temperature", 0.7),
            max_tokens=CONFIG.get("max_tokens", 2000)
        )

        # 保存AI响应
        if use_database_for_this_request:
            try:
                chat_db = get_chat_db()
                # 保存AI响应
                ai_message_id = chat_db.save_chat_message(session_id, userid, "assistant", response)
                
                # 重新获取完整历史
                updated_messages = chat_db.message_manager.get_session_messages(session_id, 100)
                history = []
                for msg in updated_messages:
                    # 处理timestamp字段
                    timestamp = None
                    if msg.get("created_at"):
                        try:
                            if hasattr(msg["created_at"], 'timestamp'):
                                # 是datetime对象
                                timestamp = msg["created_at"].timestamp()
                            else:
                                # 是字符串，尝试转换
                                from datetime import datetime
                                dt = datetime.fromisoformat(str(msg["created_at"]).replace('Z', '+00:00'))
                                timestamp = dt.timestamp()
                        except:
                            timestamp = time.time()
                    else:
                        timestamp = time.time()
                    
                    history.append({
                        "role": msg["role"],
                        "content": msg["content"],
                        "timestamp": timestamp
                    })
            except Exception as e:
                log_error(f"保存AI响应到数据库失败: {e}", exc_info=True)
        else:
            # 内存存储
            session_key = f"{userid}_{session_id}"
            user_sessions[session_key]["history"].append(
                {"role": "assistant", "content": response, "timestamp": time.time()}
            )
            history = user_sessions[session_key]["history"]

        # 返回响应
        result = {"response": response, "history": history}

        # 记录成功的聊天请求日志
        request_duration = time.time() - request_start_time
        log_user_operation(
            user_id=userid,
            operation="chat_success",
            session_id=session_id,
            details={
                "request_duration": request_duration,
                "response_length": len(response),
                "history_count": len(history)
            }
        )
        log_performance("chat_request", request_duration, user_id=userid, session_id=session_id)

        return jsonify(result)

    except Exception as e:
        error_response = f"处理您的请求时发生错误: {str(e)}"

        # 记录错误日志
        request_duration = time.time() - request_start_time
        log_error(f"聊天处理异常: {str(e)}", exc_info=True, user_id=userid, session_id=session_id)
        log_user_operation(
            user_id=userid,
            operation="chat_error",
            session_id=session_id,
            details={
                "request_duration": request_duration,
                "error": str(e)
            }
        )

        # 保存错误响应
        if use_database_for_this_request:
            try:
                chat_db = get_chat_db()
                chat_db.save_chat_message(session_id, userid, "assistant", error_response)
            except:
                pass
        else:
            session_key = f"{userid}_{session_id}"
            if session_key in user_sessions:
                user_sessions[session_key]["history"].append(
                    {"role": "assistant", "content": error_response, "timestamp": time.time()}
                )
                history = user_sessions[session_key]["history"]
            else:
                history = []

        return jsonify({"response": error_response, "history": history})


@app.route("/chat/stream", methods=["POST"])
def chat_stream():
    """处理流式聊天请求 - 服务器端事件流接口"""
    from flask import Response
    import json
    
    data = request.json
    
    # 支持两种请求格式
    if "history" in data:
        # 历史记录格式
        history = data.get("history", [])
        # 从历史记录中获取最后一条用户消息作为当前消息
        user_message = None
        for msg in reversed(history):
            if msg.get("role") == "user":
                user_message = msg.get("content")
                break
        
        # 使用稳定的会话ID生成策略
        # 如果有session_id参数则使用，否则基于对话内容生成稳定的ID
        session_id = data.get("session_id")
        if not session_id:
            # 基于对话的第一条用户消息生成稳定的session_id
            first_user_msg = None
            for msg in history:
                if msg.get("role") == "user":
                    first_user_msg = msg.get("content", "")
                    break
            
            if first_user_msg:
                # 使用第一条用户消息的哈希值生成稳定的session_id
                import hashlib
                content_hash = hashlib.md5(first_user_msg.encode('utf-8')).hexdigest()[:8]
                session_id = f"stream_stable_{content_hash}"
            else:
                # 降级方案
                session_id = f"stream_{int(time.time())}_{hash(str(history)) % 10000}"
        
        userid = data.get("userid", "stream_user")
        

        
    else:
        # 传统格式
        session_id = data.get("session_id")
        userid = data.get("userid", "default_user")
        user_message = data.get("message")
        history = data.get("history", [])
    
    if not user_message:
        return jsonify({"error": "Message required"}), 400

    # =====================================
    # 🛡️ 后端安全检查
    # =====================================
    try:
        security_check = check_message_security_backend(user_message, userid, session_id)
        
        if not security_check["is_safe"] and security_check["should_block"]:
            # 记录安全拦截事件
            log_user_operation(
                user_id=userid,
                operation="chat_stream_blocked_security",
                session_id=session_id,
                details={
                    "reason": security_check["reason"],
                    "severity": security_check["severity"],
                    "violations": security_check.get("violations", [])
                }
            )
            
            # 返回安全错误流
            def security_error_stream():
                error_data = {
                    "type": "error",
                    "data": f"🛡️ 安全检查失败: {security_check['reason']}"
                }
                yield f"data: {json.dumps(error_data)}\n\n"
            
            return Response(security_error_stream(), content_type='text/plain; charset=utf-8')
            
    except Exception as security_error:
        log_error(f"流式安全检查异常: {security_error}", exc_info=True, user_id=userid, session_id=session_id)
    
    # 🔧 会话一致性检查（优化判断逻辑）
    session_stable = (
        session_id and 
        len(session_id) > 10 and
        not session_id.startswith("stream_") and
        ('_' in session_id)  # 前端生成的sessionId格式包含下划线
    )
    

    
    def generate_stream():
        """生成流式响应"""
        try:
            # 🔧 第一步：分析对话上下文（必须在api_data构建前完成）
            api_start_time = time.time()
            headers = {"Content-Type": "application/json"}
            
            # 🔧 分析对话中是否包含数据查询（安全处理None内容）
            has_data_query = False
            data_query_content = ""
            for msg in history:
                if msg.get("role") == "user":
                    content = msg.get("content") or ""
                    if content and any(keyword in content.lower() for keyword in ["查", "数据", "开工率", "L5D98", "中山永宁"]):
                        has_data_query = True
                        data_query_content = content
                        break
            
            # 🔧 检查是否有可视化请求（安全处理None内容）
            visualization_requests = []
            for msg in history:
                if msg.get("role") == "user":
                    content = msg.get("content") or ""
                    if content and any(keyword in content.lower() for keyword in ["可视化", "图表", "画图", "绘制"]):
                        visualization_requests.append(content)
            
            # 使用OpenAI兼容客户端进行流式调用
            openai_client = get_openai_client()

            # 调用OpenAI兼容的流式接口
            stream_response = openai_client.chat_completion(
                history=history,
                temperature=CONFIG.get("temperature", 0.7),
                max_tokens=CONFIG.get("max_tokens", 2000),
                stream=True
            )

            # 处理OpenAI兼容的流式响应
            full_response = ""
            new_messages = []

            for chunk in stream_response:
                chunk_type = chunk.get("type")
                chunk_data = chunk.get("data")

                if chunk_type == "token":
                    # 处理token流
                    full_response += chunk_data
                    yield f"data: {json.dumps({'type': 'token', 'data': chunk_data})}\n\n"

                elif chunk_type == "finish":
                    # 处理完成事件
                    finish_data = chunk_data
                    full_content = finish_data.get("full_content", full_response)

                    # 构建新消息列表
                    new_messages = history + [{"role": "assistant", "content": full_content}]

                    # 发送完成事件
                    yield f"data: {json.dumps({'type': 'turn_end', 'data': {'new_messages': new_messages}})}\n\n"

                    # 💾 保存到数据库（如果使用数据库且会话稳定）
                    if CONFIG["use_database"] and session_stable:
                        try:
                            chat_db = get_chat_db()
                            # 保存用户消息（如果还没保存）
                            chat_db.save_chat_message(session_id, userid, "user", user_message)

                            # 保存AI响应
                            if full_content:
                                chat_db.save_chat_message(session_id, userid, "assistant", full_content)
                        except Exception as e:
                            log_error(f"保存消息到数据库失败: {e}", exc_info=True)

                    break

                elif chunk_type == "error":
                    # 处理错误
                    error_msg = chunk_data
                    yield f"data: {json.dumps({'type': 'error', 'data': error_msg})}\n\n"
                    return

        except Exception as e:
            log_error(f"OpenAI流式API调用异常: {e}", exc_info=True)
            error_data = {"type": "error", "data": f"❌ 系统错误，处理请求时发生异常: {str(e)}"}
            yield f"data: {json.dumps(error_data)}\n\n"
    
    return Response(generate_stream(), content_type='text/plain; charset=utf-8')


@socketio.on("connect")
def handle_connect():
    """处理WebSocket连接"""
    pass


@socketio.on("disconnect")
def handle_disconnect():
    """处理WebSocket断开连接"""
    pass


def extract_final_response(new_messages):
    """从消息列表中提取最终的助手响应"""
    # 倒序遍历消息，找到最后一个助手响应
    for msg in reversed(new_messages):
        if msg.get('role') == 'assistant' and msg.get('content') is not None:
            return msg
    return None

# 旧的get_model_response_stream函数已移除，现在使用OpenAI兼容客户端









# ===========================================
# 安全防护API接口
# ===========================================

@app.route("/api/security/config", methods=["GET"])
def get_security_config():
    """获取安全配置信息"""
    try:
        if not CONFIG["use_database"]:
            return jsonify({
                "status": "error",
                "message": "数据库未启用",
                "data": {"keywords": {}, "rules": {}}
            })
        
        chat_db = get_chat_db()
        if not chat_db:
            return jsonify({
                "status": "error",
                "message": "数据库连接失败",
                "data": {"keywords": {}, "rules": {}}
            })
        
        # 获取安全配置
        security_config = chat_db.security_manager.get_security_config()
        
        log_api_call(
            endpoint="/api/security/config",
            method="GET",
            status_code=200,
            response_length=len(str(security_config))
        )
        
        return jsonify({
            "status": "success",
            "message": "获取安全配置成功",
            "data": security_config
        })
        
    except Exception as e:
        log_error(f"获取安全配置失败: {e}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"获取安全配置失败: {str(e)}",
            "data": {"keywords": {}, "rules": {}}
        }), 500


@app.route("/api/security/check", methods=["POST"])
def check_message_security():
    """检查消息安全性"""
    try:
        data = request.json
        message = data.get("message", "")
        userid = data.get("userid", "anonymous")
        session_id = data.get("session_id", "")
        
        if not message:
            return jsonify({
                "status": "error",
                "message": "消息内容不能为空",
                "data": {"is_safe": False, "violations": []}
            })
        
        # 基础长度检查
        if len(message) > 3000:
            return jsonify({
                "status": "warning",
                "message": "消息过长",
                "data": {
                    "is_safe": False,
                    "violations": [{
                        "type": "message_too_long",
                        "severity": "medium",
                        "description": "消息长度超过限制"
                    }]
                }
            })
        
        violations = []
        
        # 如果启用数据库，进行数据库检查
        if CONFIG["use_database"]:
            chat_db = get_chat_db()
            if chat_db:
                try:
                    # 获取敏感词列表
                    keywords_by_category = chat_db.security_manager.get_keywords_by_category()
                    
                    # 检查敏感词
                    message_lower = message.lower()
                    detected_keywords = []
                    
                    for category, keywords in keywords_by_category.items():
                        for keyword in keywords:
                            if keyword.lower() in message_lower:
                                detected_keywords.append({
                                    "keyword": keyword,
                                    "category": category
                                })
                    
                    # 如果检测到敏感词，添加到违规列表
                    if detected_keywords:
                        violations.append({
                            "type": "sensitive_keywords",
                            "severity": "medium",
                            "description": "检测到敏感词汇",
                            "keywords": detected_keywords
                        })
                        
                        # 记录安全事件
                        try:
                            chat_db.security_manager.log_security_event(
                                userid=userid,
                                session_id=session_id,
                                event_type="keyword_violation",
                                severity="medium",
                                content=message[:200],  # 只记录前200字符
                                detected_keywords=[kw["keyword"] for kw in detected_keywords],
                                action_taken="blocked" if len(detected_keywords) > 2 else "warned",
                                ip_address=request.remote_addr,
                                user_agent=request.headers.get('User-Agent', ''),
                                metadata={"total_keywords": len(detected_keywords)}
                            )
                        except Exception as log_error:
                            log_error(f"记录安全事件失败: {log_error}", exc_info=True)
                
                except Exception as db_error:
                    log_error(f"数据库安全检查失败: {db_error}", exc_info=True)
                    violations.append({
                        "type": "database_error",
                        "severity": "low",
                        "description": "数据库检查失败，使用基础检查"
                    })
        
        # 基础敏感词检查（作为备用）- 只检查高风险词汇
        basic_sensitive_words = [
            "system:", "execute", "hack", "exploit", 
            "jailbreak", "bypass", "ignore previous", "forget previous"
        ]
        
        message_lower = message.lower()
        basic_detected = [word for word in basic_sensitive_words if word in message_lower]
        
        if basic_detected and not any(v["type"] == "sensitive_keywords" for v in violations):
            violations.append({
                "type": "basic_sensitive_keywords",
                "severity": "medium",
                "description": "检测到基础敏感词",
                "keywords": basic_detected
            })
        
        # 判断是否安全
        is_safe = len(violations) == 0
        severity_level = "low"
        
        if violations:
            high_severity = any(v.get("severity") == "high" for v in violations)
            medium_severity = any(v.get("severity") == "medium" for v in violations)
            
            if high_severity:
                severity_level = "high"
            elif medium_severity:
                severity_level = "medium"
        
        log_api_call(
            endpoint="/api/security/check",
            method="POST",
            status_code=200,
            response_length=len(str(violations))
        )
        
        return jsonify({
            "status": "success" if is_safe else "warning",
            "message": "消息安全检查完成",
            "data": {
                "is_safe": is_safe,
                "severity": severity_level,
                "violations": violations,
                "total_violations": len(violations)
            }
        })
        
    except Exception as e:
        log_error(f"安全检查失败: {e}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"安全检查失败: {str(e)}",
            "data": {"is_safe": False, "violations": []}
        }), 500


@app.route("/api/security/keywords", methods=["GET"])
def get_security_keywords():
    """获取敏感词列表"""
    try:
        category = request.args.get("category")
        
        if not CONFIG["use_database"]:
            return jsonify({
                "status": "error",
                "message": "数据库未启用",
                "data": {"keywords": []}
            })
        
        chat_db = get_chat_db()
        if not chat_db:
            return jsonify({
                "status": "error",
                "message": "数据库连接失败",
                "data": {"keywords": []}
            })
        
        # 获取敏感词
        keywords = chat_db.security_manager.get_active_keywords(category)
        
        return jsonify({
            "status": "success",
            "message": "获取敏感词成功",
            "data": {
                "keywords": keywords,
                "total": len(keywords),
                "category": category
            }
        })
        
    except Exception as e:
        log_error(f"获取敏感词失败: {e}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"获取敏感词失败: {str(e)}",
            "data": {"keywords": []}
        }), 500


@app.route("/api/security/user/status", methods=["GET"])
def get_user_security_status():
    """获取用户安全状态"""
    try:
        userid = request.args.get("userid")
        
        if not userid:
            return jsonify({
                "status": "error",
                "message": "用户ID不能为空",
                "data": {"status": None}
            })
        
        if not CONFIG["use_database"]:
            return jsonify({
                "status": "success",
                "message": "数据库未启用，返回默认状态",
                "data": {
                    "status": {
                        "userid": userid,
                        "suspicious_score": 0,
                        "warning_count": 0,
                        "is_blocked": False,
                        "violation_count": 0
                    }
                }
            })
        
        chat_db = get_chat_db()
        if not chat_db:
            return jsonify({
                "status": "error",
                "message": "数据库连接失败",
                "data": {"status": None}
            })
        
        # 获取用户安全状态
        user_status = chat_db.security_manager.get_user_security_status(userid)
        
        return jsonify({
            "status": "success",
            "message": "获取用户安全状态成功",
            "data": {
                "status": user_status,
                "userid": userid
            }
        })
        
    except Exception as e:
        log_error(f"获取用户安全状态失败: {e}", exc_info=True)
        return jsonify({
            "status": "error",
            "message": f"获取用户安全状态失败: {str(e)}",
            "data": {"status": None}
        }), 500


@app.route("/api/security/keywords/add", methods=["POST"])
@admin_required
def add_security_keyword():
    """添加敏感词（管理员功能）"""
    try:
            
        data = request.json
        keyword = data.get("keyword", "").strip()
        
        # 支持新旧两种格式：category_id (新) 和 category (旧)
        category_id = data.get("category_id")
        category_name = data.get("category", "").strip()
        
        severity = data.get("severity", "high")
        description = data.get("description", "")
        is_regex = data.get("is_regex", False)
        created_by = data.get("created_by", "admin")
        
        if not keyword:
            return jsonify({
                "error": "敏感词内容不能为空"
            }), 400
        
        # 如果没有提供分类，默认使用"内部敏感"分类
        if not category_id and not category_name:
            category_name = "internal_sensitive"
        
        if not CONFIG["use_database"]:
            return jsonify({
                "error": "数据库未启用"
            }), 503
        
        chat_db = get_chat_db()
        if not chat_db:
            return jsonify({
                "error": "数据库连接失败"
            }), 500
        
        # 优先使用 category_id，如果没有则使用 category_name
        if category_id:
            keyword_id = chat_db.security_manager.add_keyword_by_category_id(
                keyword=keyword,
                category_id=category_id,
                severity=severity,
                is_regex=is_regex,
                description=description,
                created_by=created_by
            )
        else:
            keyword_id = chat_db.security_manager.add_keyword(
                keyword=keyword,
                category_name=category_name,
                severity=severity,
                is_regex=is_regex,
                description=description,
                created_by=created_by
            )
        
        if keyword_id:
            log_user_operation(f"添加敏感词: {keyword}", created_by, session_id=None, 
                             category='security', details=f"分类ID: {category_id or 'name:' + category_name}, 严重级别: {severity}")
            return jsonify({
                "success": True,
                "message": "敏感词添加成功",
                "keyword_id": keyword_id
            })
        else:
            return jsonify({
                "error": "敏感词添加失败"
            }), 500
        
    except Exception as e:
        log_error(f"添加敏感词失败: {e}", exc_info=True)
        return jsonify({
            "error": f"添加敏感词失败: {str(e)}"
        }), 500


@app.route("/api/security/categories", methods=["GET"])
@admin_required
def get_security_categories():
    """获取安全分类列表"""
    
    if CONFIG["use_database"]:
        try:
            chat_db = get_chat_db()
            categories = chat_db.security_manager.get_categories()
            
            # 添加中文分类名称映射
            category_chinese_names = {
                'role_playing': '角色扮演',
                'system_commands': '系统指令',
                'information_leak': '信息泄露',
                'bypass_attempts': '绕过尝试',
                'internal_sensitive': '内部敏感'
            }
            
            # 为每个分类添加中文名称
            for category in categories:
                category['chinese_name'] = category_chinese_names.get(category['name'], category['name'])
            
            return jsonify(categories)
        except Exception as e:
            log_error(f"获取安全分类失败: {e}", exc_info=True)
            return jsonify({"error": f"获取分类失败: {str(e)}"}), 500
    else:
        return jsonify({"error": "数据库未启用"}), 503

@app.route("/api/security/keywords/list", methods=["GET"])
@admin_required
def get_keywords_list():
    """获取敏感词列表"""
    
    if CONFIG["use_database"]:
        try:
            chat_db = get_chat_db()
            keywords = chat_db.security_manager.get_all_keywords()
            statistics = chat_db.security_manager.get_keywords_statistics()
            
            # 添加中文分类名称映射
            category_chinese_names = {
                'role_playing': '角色扮演',
                'system_commands': '系统指令',
                'information_leak': '信息泄露',
                'bypass_attempts': '绕过尝试',
                'internal_sensitive': '内部敏感'
            }
            
            # 为每个敏感词添加中文分类名称
            for keyword in keywords:
                keyword['category_chinese_name'] = category_chinese_names.get(keyword.get('category_name', ''), keyword.get('category_name', ''))
            
            return jsonify({
                "keywords": keywords,
                "statistics": statistics
            })
        except Exception as e:
            log_error(f"获取敏感词列表失败: {e}", exc_info=True)
            return jsonify({"error": f"获取敏感词列表失败: {str(e)}"}), 500
    else:
        return jsonify({"error": "数据库未启用"}), 503

@app.route("/api/security/keywords/<int:keyword_id>", methods=["PUT"])
@admin_required
def update_keyword(keyword_id):
    """更新敏感词"""
    try:
        data = request.json
        if not data:
            return jsonify({"error": "请求数据不能为空"}), 400

        # 从JWT token中获取用户信息
        userid = request.current_user.get('userid', 'admin') if hasattr(request, 'current_user') else 'admin'

        if not CONFIG["use_database"]:
            return jsonify({"error": "数据库未启用"}), 503

        chat_db = get_chat_db()
        if not chat_db:
            return jsonify({"error": "数据库连接失败"}), 500

        success = chat_db.security_manager.update_keyword(
            keyword_id=keyword_id,
            keyword=data.get("keyword"),
            category_id=data.get("category_id"),
            severity=data.get("severity"),
            is_regex=data.get("is_regex"),
            is_active=data.get("is_active"),
            description=data.get("description")
        )

        if success:
            log_user_operation(f"更新敏感词ID: {keyword_id}", userid, session_id=None,
                             category='security', details=f"新内容: {data.get('keyword')}")
            return jsonify({"success": True, "message": "敏感词更新成功"})
        else:
            return jsonify({"error": "敏感词更新失败"}), 500

    except Exception as e:
        log_error(f"更新敏感词失败: {e}", exc_info=True)
        return jsonify({"error": f"更新敏感词失败: {str(e)}"}), 500

@app.route("/api/security/keywords/<int:keyword_id>", methods=["DELETE"])
@admin_required
def delete_keyword(keyword_id):
    """删除敏感词"""
    try:
        # 从JWT token中获取用户信息
        userid = request.current_user.get('userid', 'admin') if hasattr(request, 'current_user') else 'admin'

        if not CONFIG["use_database"]:
            return jsonify({"error": "数据库未启用"}), 503

        chat_db = get_chat_db()
        if not chat_db:
            return jsonify({"error": "数据库连接失败"}), 500

        success = chat_db.security_manager.delete_keyword(keyword_id)

        if success:
            log_user_operation(f"删除敏感词ID: {keyword_id}", userid, session_id=None,
                             category='security')
            return jsonify({"success": True, "message": "敏感词删除成功"})
        else:
            return jsonify({"error": "敏感词删除失败"}), 500

    except Exception as e:
        log_error(f"删除敏感词失败: {e}", exc_info=True)
        return jsonify({"error": f"删除敏感词失败: {str(e)}"}), 500

@app.route("/api/security/keywords/<int:keyword_id>/toggle", methods=["POST"])
@admin_required
def toggle_keyword_status(keyword_id):
    """切换敏感词状态"""
    try:
        # 从JWT token中获取用户信息
        userid = request.current_user.get('userid', 'admin') if hasattr(request, 'current_user') else 'admin'

        if not CONFIG["use_database"]:
            return jsonify({"error": "数据库未启用"}), 503

        chat_db = get_chat_db()
        if not chat_db:
            return jsonify({"error": "数据库连接失败"}), 500

        success = chat_db.security_manager.toggle_keyword_status(keyword_id)

        if success:
            log_user_operation(f"切换敏感词状态ID: {keyword_id}", userid, session_id=None,
                             category='security')
            return jsonify({"success": True, "message": "敏感词状态切换成功"})
        else:
            return jsonify({"error": "敏感词状态切换失败"}), 500

    except Exception as e:
        log_error(f"切换敏感词状态失败: {e}", exc_info=True)
        return jsonify({"error": f"切换敏感词状态失败: {str(e)}"}), 500

@app.route("/api/security/keywords/batch/delete", methods=["POST"])
@admin_required
def batch_delete_keywords():
    """批量删除敏感词"""
    try:
        data = request.json
        if not data:
            return jsonify({"error": "请求数据不能为空"}), 400

        keyword_ids = data.get("keyword_ids", [])
        # 从JWT token中获取用户信息
        userid = request.current_user.get('userid', 'admin') if hasattr(request, 'current_user') else 'admin'

        if not keyword_ids:
            return jsonify({"error": "请选择要删除的敏感词"}), 400

        if not CONFIG["use_database"]:
            return jsonify({"error": "数据库未启用"}), 503

        chat_db = get_chat_db()
        if not chat_db:
            return jsonify({"error": "数据库连接失败"}), 500

        affected_count = chat_db.security_manager.batch_delete_keywords(keyword_ids)

        log_user_operation(f"批量删除敏感词: {len(keyword_ids)}个", userid, session_id=None,
                         category='security', details=f"IDs: {keyword_ids}")
        return jsonify({
            "success": True,
            "message": f"批量删除成功",
            "affected_count": affected_count
        })

    except Exception as e:
        log_error(f"批量删除敏感词失败: {e}", exc_info=True)
        return jsonify({"error": f"批量删除失败: {str(e)}"}), 500

@app.route("/api/security/keywords/batch/toggle", methods=["POST"])
@admin_required
def batch_toggle_keywords():
    """批量切换敏感词状态"""
    try:
        data = request.json
        if not data:
            return jsonify({"error": "请求数据不能为空"}), 400

        keyword_ids = data.get("keyword_ids", [])
        # 从JWT token中获取用户信息
        userid = request.current_user.get('userid', 'admin') if hasattr(request, 'current_user') else 'admin'

        if not keyword_ids:
            return jsonify({"error": "请选择要操作的敏感词"}), 400

        if not CONFIG["use_database"]:
            return jsonify({"error": "数据库未启用"}), 503

        chat_db = get_chat_db()
        if not chat_db:
            return jsonify({"error": "数据库连接失败"}), 500

        affected_count = chat_db.security_manager.batch_toggle_keywords(keyword_ids)

        log_user_operation(f"批量切换敏感词状态: {len(keyword_ids)}个", userid, session_id=None,
                         category='security', details=f"IDs: {keyword_ids}")
        return jsonify({
            "success": True,
            "message": f"批量操作成功",
            "affected_count": affected_count
        })

    except Exception as e:
        log_error(f"批量切换敏感词状态失败: {e}", exc_info=True)
        return jsonify({"error": f"批量操作失败: {str(e)}"}), 500


def check_message_security_backend(message, userid, session_id):
    """后端消息安全检查"""
    try:
        if not message or len(message.strip()) == 0:
            return {
                "is_safe": False,
                "reason": "消息内容不能为空",
                "severity": "medium",
                "should_block": True
            }
        
        # 基础长度检查
        if len(message) > 5000:  # 后端检查更严格
            return {
                "is_safe": False,
                "reason": "消息内容过长，请分段发送",
                "severity": "medium",
                "should_block": True
            }
        
        violations = []
        detected_keywords = []
        
        # 如果启用数据库，进行敏感词检查
        if CONFIG["use_database"]:
            try:
                chat_db = get_chat_db()
                if chat_db and hasattr(chat_db, 'security_manager'):
                    # 获取敏感词
                    keywords_by_category = chat_db.security_manager.get_keywords_by_category()
                    
                    message_lower = message.lower()
                    for category, keywords in keywords_by_category.items():
                        for keyword in keywords:
                            if keyword.lower() in message_lower:
                                detected_keywords.append({
                                    "keyword": keyword,
                                    "category": category
                                })
                                violations.append({
                                    "type": "sensitive_keyword",
                                    "keyword": keyword,
                                    "category": category,
                                    "severity": "medium"
                                })
                    
                    # 记录安全事件
                    if detected_keywords:
                        try:
                            chat_db.security_manager.log_security_event(
                                userid=userid,
                                session_id=session_id,
                                event_type="keyword_violation_backend",
                                severity="medium" if len(detected_keywords) <= 2 else "high",
                                content=message[:300],  # 限制内容长度
                                detected_keywords=[kw["keyword"] for kw in detected_keywords],
                                action_taken="blocked" if len(detected_keywords) > 3 else "warned",
                                ip_address=request.remote_addr,
                                user_agent=request.headers.get('User-Agent', ''),
                                metadata={
                                    "backend_check": True,
                                    "total_keywords": len(detected_keywords),
                                    "categories": list(set([kw["category"] for kw in detected_keywords]))
                                }
                            )
                        except Exception as log_err:
                            log_error(f"记录安全事件失败: {log_err}", exc_info=True)
                            
            except Exception as db_error:
                log_error(f"数据库安全检查失败: {db_error}", exc_info=True)
        
        # 基础敏感词检查（作为备用）- 只检查真正的高风险词汇
        basic_high_risk_keywords = [
            "system:", "execute", "shell", "terminal", 
            "jailbreak", "bypass", "hack", "exploit", "backdoor"
        ]
        
        message_lower = message.lower()
        basic_detected = []
        for keyword in basic_high_risk_keywords:
            if keyword in message_lower:
                basic_detected.append(keyword)
                violations.append({
                    "type": "high_risk_keyword",
                    "keyword": keyword,
                    "severity": "high"
                })
        
        # 危险模式检查
        dangerous_patterns = [
            r'<!--.*?-->',  # HTML注释
            r'<script.*?>.*?</script>',  # Script标签
            r'<iframe.*?>.*?</iframe>',  # Iframe标签
            r'javascript:',  # JavaScript协议
            r'data:.*?base64',  # Base64数据URI
        ]
        
        import re
        for pattern in dangerous_patterns:
            if re.search(pattern, message, re.IGNORECASE | re.DOTALL):
                violations.append({
                    "type": "dangerous_pattern",
                    "pattern": pattern,
                    "severity": "high"
                })
        
        # 判断是否应该阻止
        high_severity_count = len([v for v in violations if v.get("severity") == "high"])
        medium_severity_count = len([v for v in violations if v.get("severity") == "medium"])
        
        should_block = high_severity_count > 0 or medium_severity_count > 3
        
        if violations:
            return {
                "is_safe": False,
                "reason": f"检测到 {len(violations)} 个安全问题",
                "severity": "high" if high_severity_count > 0 else "medium",
                "should_block": should_block,
                "violations": violations,
                "detected_keywords": detected_keywords
            }
        
        return {
            "is_safe": True,
            "reason": "消息通过安全检查",
            "severity": "low",
            "should_block": False
        }
        
    except Exception as e:
        log_error(f"后端安全检查异常: {e}", exc_info=True)
        return {
            "is_safe": True,  # 发生错误时默认允许，避免影响正常使用
            "reason": "安全检查出现异常，已记录日志",
            "severity": "low",
            "should_block": False
        }


def cleanup_resources():
    """清理资源"""
    try:
        # 关闭数据库连接池
        if CONFIG["use_database"]:
            chat_db = get_chat_db()
            chat_db.db_manager.close_pool()
            log_app_event("数据库连接池已关闭", level='info')
        
        # 关闭Redis连接
        if cache_manager.connection_pool:
            cache_manager.connection_pool.disconnect()
            log_app_event("Redis连接池已关闭", level='info')
            
    except Exception as e:
        log_error(f"资源清理异常: {e}", exc_info=True)

import atexit
atexit.register(cleanup_resources)

if __name__ == "__main__":
    # 记录应用启动日志
    log_app_event("昆仑问策应用开始启动", level='info')
    
    # 获取当前脚本所在目录
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 构建证书的绝对路径
    cert_path = os.path.join(base_dir, 'cert.pem')
    key_path = os.path.join(base_dir , 'key.pem')

    # 检查证书文件是否存在
    if not (os.path.exists(cert_path) and os.path.exists(key_path)):
        log_app_event("证书文件不存在，使用HTTP模式", level='warning')
        # exit(1)
    
    try:
        # http
        log_app_event("昆仑问策应用启动成功 - HTTP模式 (0.0.0.0:5000)", level='info')
        socketio.run(app, host='0.0.0.0', port= 5000, debug=False ) 
        # https
        # log_app_event("昆仑问策应用启动成功 - HTTPS模式 (0.0.0.0:8443)", level='info')
        # socketio.run(app, host="0.0.0.0",port=8443,ssl_context=(cert_path, key_path),debug=False)
    except KeyboardInterrupt:
        log_app_event("收到中断信号，正在关闭应用", level='info')
        cleanup_resources()
    except Exception as e:
        log_error(f"应用启动失败: {str(e)}", exc_info=True)
        cleanup_resources()
        raise 
