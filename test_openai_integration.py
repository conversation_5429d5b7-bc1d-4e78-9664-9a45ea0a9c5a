#!/usr/bin/env python3
"""
OpenAI兼容接口集成测试脚本
用于验证OpenAI兼容接口的功能
"""

import os
import sys
import time
import json
import requests
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_openai_client_import():
    """测试OpenAI客户端模块导入"""
    print("🔍 测试OpenAI客户端模块导入...")
    try:
        from openai_client import OpenAICompatibleClient, get_openai_client, init_openai_client
        print("✅ OpenAI客户端模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ OpenAI客户端模块导入失败: {e}")
        return False

def test_openai_client_initialization():
    """测试OpenAI客户端初始化"""
    print("\n🔍 测试OpenAI客户端初始化...")
    try:
        from openai_client import OpenAICompatibleClient
        
        # 使用测试配置初始化客户端
        client = OpenAICompatibleClient(
            api_key="test-key",
            base_url="https://api.openai.com/v1",
            model="gpt-3.5-turbo"
        )
        
        print(f"✅ OpenAI客户端初始化成功")
        print(f"   - API URL: {client.chat_url}")
        print(f"   - Model: {client.model}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI客户端初始化失败: {e}")
        return False

def test_message_format_conversion():
    """测试消息格式转换"""
    print("\n🔍 测试消息格式转换...")
    try:
        from openai_client import OpenAICompatibleClient
        
        client = OpenAICompatibleClient()
        
        # 测试历史消息转换
        history = [
            {"role": "user", "content": "你好"},
            {"role": "assistant", "content": "你好！有什么可以帮助你的吗？"},
            {"role": "user", "content": "请介绍一下Python"}
        ]
        
        messages = client._convert_history_to_messages(history)
        
        print(f"✅ 消息格式转换成功")
        print(f"   - 原始历史消息数: {len(history)}")
        print(f"   - 转换后消息数: {len(messages)}")
        print(f"   - 示例消息: {messages[0] if messages else 'None'}")
        return True
        
    except Exception as e:
        print(f"❌ 消息格式转换失败: {e}")
        return False

def test_mock_api_call():
    """测试模拟API调用（不实际发送请求）"""
    print("\n🔍 测试模拟API调用...")
    try:
        from openai_client import OpenAICompatibleClient
        
        # 使用无效的URL来测试错误处理
        client = OpenAICompatibleClient(
            api_key="test-key",
            base_url="http://invalid-url-for-testing",
            model="gpt-3.5-turbo"
        )
        
        messages = [{"role": "user", "content": "测试消息"}]
        
        try:
            # 这应该会失败，但我们测试的是错误处理
            response = client.chat_completion(messages=messages, timeout=1)
        except Exception as expected_error:
            print(f"✅ API调用错误处理正常: {type(expected_error).__name__}")
            return True
        
        print("⚠️ 预期的错误没有发生")
        return False
        
    except Exception as e:
        print(f"❌ 模拟API调用测试失败: {e}")
        return False

def test_app_integration():
    """测试应用集成"""
    print("\n🔍 测试应用集成...")
    try:
        # 检查app.py是否能正常导入OpenAI客户端
        import app
        
        # 检查配置是否正确加载
        config = app.CONFIG
        required_keys = ['model', 'api_key', 'api_url', 'temperature', 'max_tokens']
        
        for key in required_keys:
            if key not in config:
                print(f"❌ 配置缺少必需的键: {key}")
                return False
        
        print("✅ 应用集成测试通过")
        print(f"   - 模型: {config['model']}")
        print(f"   - API URL: {config['api_url']}")
        print(f"   - 温度: {config['temperature']}")
        print(f"   - 最大Token: {config['max_tokens']}")
        return True
        
    except Exception as e:
        print(f"❌ 应用集成测试失败: {e}")
        return False

def test_environment_config():
    """测试环境配置"""
    print("\n🔍 测试环境配置...")
    try:
        # 检查必需的环境变量
        required_vars = [
            'OPENAI_API_KEY',
            'OPENAI_API_URL', 
            'OPENAI_MODEL',
            'MODEL_TEMPERATURE',
            'MODEL_MAX_TOKENS'
        ]
        
        missing_vars = []
        for var in required_vars:
            value = os.getenv(var)
            if not value:
                missing_vars.append(var)
            else:
                print(f"   - {var}: {value}")
        
        if missing_vars:
            print(f"⚠️ 缺少环境变量: {missing_vars}")
            print("   请在.env文件中配置这些变量")
        else:
            print("✅ 环境配置检查通过")
        
        return len(missing_vars) == 0
        
    except Exception as e:
        print(f"❌ 环境配置测试失败: {e}")
        return False

def test_api_endpoint_format():
    """测试API端点格式"""
    print("\n🔍 测试API端点格式...")
    try:
        from openai_client import OpenAICompatibleClient
        
        # 测试不同的base_url格式
        test_cases = [
            ("https://api.openai.com", "https://api.openai.com/v1/chat/completions"),
            ("https://api.openai.com/", "https://api.openai.com/v1/chat/completions"),
            ("https://api.openai.com/v1", "https://api.openai.com/v1/chat/completions"),
            ("https://api.openai.com/v1/", "https://api.openai.com/v1/chat/completions"),
            ("http://localhost:8000", "http://localhost:8000/v1/chat/completions"),
        ]
        
        for base_url, expected_chat_url in test_cases:
            client = OpenAICompatibleClient(base_url=base_url)
            if client.chat_url != expected_chat_url:
                print(f"❌ URL格式错误: {base_url} -> {client.chat_url} (期望: {expected_chat_url})")
                return False
        
        print("✅ API端点格式测试通过")
        return True
        
    except Exception as e:
        print(f"❌ API端点格式测试失败: {e}")
        return False

def test_real_api_call():
    """测试真实API调用（如果配置了有效的API密钥）"""
    print("\n🔍 测试真实API调用...")
    
    api_key = os.getenv('OPENAI_API_KEY')
    api_url = os.getenv('OPENAI_API_URL')
    
    if not api_key or api_key == 'your_openai_api_key':
        print("⚠️ 跳过真实API调用测试 - 未配置有效的API密钥")
        return True
    
    try:
        from openai_client import OpenAICompatibleClient
        
        client = OpenAICompatibleClient()
        
        # 发送简单的测试消息
        response = client.get_simple_response(
            message="请回复'测试成功'",
            timeout=10
        )
        
        if response and "测试" in response:
            print(f"✅ 真实API调用成功")
            print(f"   - 响应: {response[:100]}...")
            return True
        else:
            print(f"⚠️ API调用成功但响应异常: {response}")
            return False
        
    except Exception as e:
        print(f"❌ 真实API调用失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始OpenAI兼容接口集成测试")
    print("=" * 60)
    
    # 执行测试
    tests = [
        ("OpenAI客户端模块导入", test_openai_client_import),
        ("OpenAI客户端初始化", test_openai_client_initialization),
        ("消息格式转换", test_message_format_conversion),
        ("模拟API调用", test_mock_api_call),
        ("应用集成", test_app_integration),
        ("环境配置", test_environment_config),
        ("API端点格式", test_api_endpoint_format),
        ("真实API调用", test_real_api_call),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "="*60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！OpenAI兼容接口集成成功！")
        return True
    else:
        print("❌ 部分测试失败，请检查配置和实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
