#!/usr/bin/env python3
"""
MySQL迁移测试脚本
用于验证从人大金仓到MySQL的迁移是否成功
"""

import os
import sys
import time
import json
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_mysql_connection():
    """测试MySQL连接"""
    print("🔍 测试MySQL连接...")
    try:
        import pymysql
        
        # 从环境变量获取配置
        config = {
            'host': os.getenv('MYSQL_HOST', 'localhost'),
            'port': int(os.getenv('MYSQL_PORT', 3306)),
            'user': os.getenv('MYSQL_USER', 'root'),
            'password': os.getenv('MYSQL_PASSWORD', ''),
            'database': os.getenv('MYSQL_DATABASE', 'cnpcklwc'),
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor,
        }
        
        print(f"连接配置: {config['host']}:{config['port']}/{config['database']}")
        
        connection = pymysql.connect(**config)
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION() as version")
            result = cursor.fetchone()
            print(f"✅ MySQL连接成功! 版本: {result['version']}")
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        return False

def test_database_schema():
    """测试数据库架构"""
    print("\n🔍 测试数据库架构...")
    try:
        import pymysql
        
        config = {
            'host': os.getenv('MYSQL_HOST', 'localhost'),
            'port': int(os.getenv('MYSQL_PORT', 3306)),
            'user': os.getenv('MYSQL_USER', 'root'),
            'password': os.getenv('MYSQL_PASSWORD', ''),
            'database': os.getenv('MYSQL_DATABASE', 'cnpcklwc'),
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor,
        }
        
        connection = pymysql.connect(**config)
        with connection.cursor() as cursor:
            # 检查主要表是否存在
            tables_to_check = [
                'users', 'chat_sessions', 'chat_messages',
                'security_keyword_categories', 'security_keywords',
                'security_rules', 'security_logs', 'user_security_status'
            ]
            
            cursor.execute("SHOW TABLES")
            existing_tables = [row[f'Tables_in_{config["database"]}'] for row in cursor.fetchall()]
            
            print(f"数据库中的表: {existing_tables}")
            
            missing_tables = []
            for table in tables_to_check:
                if table not in existing_tables:
                    missing_tables.append(table)
                else:
                    print(f"✅ 表 {table} 存在")
            
            if missing_tables:
                print(f"❌ 缺少表: {missing_tables}")
                return False
            else:
                print("✅ 所有必需的表都存在")
                return True
                
        connection.close()
        
    except Exception as e:
        print(f"❌ 数据库架构测试失败: {e}")
        return False

def test_database_operations():
    """测试数据库基本操作"""
    print("\n🔍 测试数据库基本操作...")
    try:
        from database import init_chat_database, get_chat_db
        
        # 初始化数据库
        success = init_chat_database()
        if not success:
            print("❌ 数据库初始化失败")
            return False
        
        print("✅ 数据库初始化成功")
        
        # 获取数据库实例
        chat_db = get_chat_db()
        if not chat_db:
            print("❌ 获取数据库实例失败")
            return False
        
        print("✅ 获取数据库实例成功")
        
        # 测试用户操作
        test_userid = "test_mysql_user"
        user = chat_db.user_manager.get_or_create_user(test_userid, "MySQL测试用户")
        if user:
            print(f"✅ 用户操作成功: {user['username']}")
        else:
            print("❌ 用户操作失败")
            return False
        
        # 测试会话操作
        test_session_id = f"test_session_{int(time.time())}"
        session_id = chat_db.session_manager.create_session(test_session_id, test_userid, "MySQL测试会话")
        if session_id:
            print(f"✅ 会话创建成功: {session_id}")
        else:
            print("❌ 会话创建失败")
            return False
        
        # 测试消息操作
        message_id = chat_db.message_manager.add_message(
            test_session_id, test_userid, "user", "这是一条MySQL测试消息"
        )
        if message_id:
            print(f"✅ 消息添加成功: {message_id}")
        else:
            print("❌ 消息添加失败")
            return False
        
        # 测试查询操作
        messages = chat_db.message_manager.get_session_messages(test_session_id)
        if messages and len(messages) > 0:
            print(f"✅ 消息查询成功: 找到 {len(messages)} 条消息")
        else:
            print("❌ 消息查询失败")
            return False
        
        print("✅ 所有数据库操作测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_connection_pool():
    """测试连接池"""
    print("\n🔍 测试连接池...")
    try:
        from database import get_chat_db
        
        chat_db = get_chat_db()
        if not chat_db:
            print("❌ 获取数据库实例失败")
            return False
        
        # 获取连接池统计
        pool_stats = chat_db.db_manager.get_pool_stats()
        print(f"连接池统计: {json.dumps(pool_stats, indent=2, ensure_ascii=False)}")
        
        if pool_stats.get('status') == 'active':
            print("✅ 连接池运行正常")
            return True
        else:
            print("❌ 连接池状态异常")
            return False
            
    except Exception as e:
        print(f"❌ 连接池测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始MySQL迁移测试")
    print("=" * 50)
    
    # 检查依赖
    try:
        import pymysql
        print("✅ PyMySQL依赖检查通过")
    except ImportError:
        print("❌ PyMySQL未安装，请运行: pip install PyMySQL")
        return False
    
    # SQLAlchemy不再必需，使用简单连接池
    
    # 执行测试
    tests = [
        ("MySQL连接测试", test_mysql_connection),
        ("数据库架构测试", test_database_schema),
        ("数据库操作测试", test_database_operations),
        ("连接池测试", test_connection_pool),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "="*50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！MySQL迁移成功！")
        return True
    else:
        print("❌ 部分测试失败，请检查配置和环境")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
